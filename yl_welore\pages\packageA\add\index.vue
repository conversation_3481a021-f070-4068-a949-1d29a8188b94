<template>
	<view class="page-container">
		<cu-custom bgColor="none" :isBack="true" :isSearch="false" :isBackAlert="true">
			<view slot="backText">返回</view>
			<view slot="content" class="title-content">
				<text class="title-text">发布</text>
			</view>
		</cu-custom>
		<!-- 发布到 -->
		<view v-if="check_fa_class" class="publish-to-card">
			<view @tap="toggleLeft" class="publish-to-content">
				<text class="publish-to-emoji cicon-discover"></text>
				<text class="publish-to-text">{{ title == '' ? no_title : title }}</text>
				<text class="cicon-forward text-gray"></text>
			</view>
		</view>
		<!-- 发布到 -->


		<!-- 标题输入区域 -->
		<view class="title-input-card" v-if="copyright.title_input_arbor == 1">
			<view class="title-input-header">
				<text class="section-title">标题</text>
			</view>
			<view v-if="get_hidden"
				style="gap: 30rpx;display: flex;align-items: center;justify-content: space-between;">
				<view style="width: 100%;">
					<input :value="title_value" @input="set_title_value" maxlength="40" class="modern-input"
						:style="'color:' + (title_color) + ';'" placeholder="标题（5-40个字）" />
				</view>
				<view v-if="is_vip == 1" @tap="color_select">
					<view class="color-select-btn">
						<text class="cicon-paint"></text>
					</view>
				</view>
			</view>
		</view>

		<!-- 涂鸦功能区域 -->
		<view class="drawing-card" v-if="tuya == true">
			<view class="drawing-header">
				<text class="section-emoji _icon-more"></text>
				<text class="section-title">涂鸦创作</text>
				<view @tap="cleardraw" class="clear-canvas-btn">
					<text class="_icon-delete"></text>
					<text>清空画布</text>
				</view>
			</view>
			<view :style="'display:' + (get_hidden == true ? 'inline' : 'none')">
				<canvas class="modern-canvas" id="canvas" canvas-id="canvas" :disable-scroll="true"
					@touchstart="canvasStart" @touchmove="canvasMove" @touchend="canvasEnd" touchcancel="canvasEnd"
					@error="canvasIdErrorCallback"></canvas>
			</view>
			<view class="color-palette">
				<view class="palette-title">
					<text class="palette-emoji _icon-more"></text>
					<text>调色板</text>
				</view>
				<view class="grid col-5 text-center">
					<view @tap="color_ssss" :data-color="item.color" class="color-item"
						:style="'background-color:' + (item.color) + ';'" v-for="(item, index) in (color_list)"
						:key="index"></view>
				</view>
			</view>
		</view>


		<!-- 语音功能区域 -->
		<view class="content-section">
			<view v-if="fa_type == 1" class="audio-card">
				<view class="audio-header">
					<text class="section-title">语音录制</text>
				</view>

				<view class="audio-player" v-if="show_audio">
					<view class="audio-controls">
						<view class="play-btn" @tap="play" v-if="(!is_voice)">
							<text class="cicon-play-arrow"></text>
						</view>
						<view class="pause-btn" @tap="stop_yin" v-if="is_voice">
							<text class="cicon-pause"></text>
						</view>
						<view class="audio-slider">
							<slider @change="sliderChange" block-size="12px" step="1" :value="is_time" :max="file_ss"
								selected-color="#1cbbb4" />
							<view class="time-display">
								<text class="current-time">{{ is_time_name }}</text>
								<text class="total-time">{{ flie_ss_name }}</text>
							</view>
						</view>
					</view>
				</view>

				<view class="recording-area">
					<view class="record-btn" @tap="touchStart" v-if="(!star_recorder)">
						<text class="record-emoji cicon-volume"></text>
						<text class="record-text">开始录音</text>
					</view>
					<view class="stop-btn" @tap="touchEnd" v-if="star_recorder">
						<text class="stop-emoji cicon-volume-off"></text>
						<text class="stop-text">停止录音</text>
					</view>
				</view>
				<view class="recording-tip">
					<text class="_icon-info"></text>
					<text> 点击开始录音(最长5分钟)</text>
				</view>
			</view>
		</view>

		<!-- 视频功能区域 -->
		<view class="content-section" v-if="fa_type == 2">
			<view class="video-card">
				<view class="video-header">
					<text class="section-title">视频上传</text>
				</view>

				<!-- 视频预览区域 -->
				<view v-if="file != '' && get_hidden" class="video-preview-container"
					style="position: relative;margin: 15px 0;">
					<video id="myVideo" :src="file" class="video-preview"></video>
					<view class="video-clear-btn" @tap="clearVideo"
						style="position: absolute;top: 8px;right: 8px;width: 30px;height: 30px;background-color: rgba(0,0,0,0.6);border-radius: 50%;display: flex;align-items: center;justify-content: center;z-index: 100;">
						<text style="color: white;font-size: 16px;" class="_icon-close"></text>
					</view>
				</view>

				<view class="video-options">
					<view class="video-option" @tap="add_video">
						<view class="option-icon">
							<text class="option-emoji cicon-play-circle"></text>
						</view>
						<view class="option-text">本地视频</view>
						<view class="option-desc">最长{{ copyright.video_setting }}s</view>
					</view>

					<view class="video-option" v-if="open_jiexi" @tap="add_jiexi">
						<view class="option-icon">
							<text class="option-emoji cicon-record"></text>
						</view>
						<view class="option-text">视频地址</view>
						<view class="option-desc">在线解析</view>
					</view>

					<view class="video-option" @tap="add_shipinhao">
						<view class="option-icon">
							<text class="option-emoji cicon-miniprogram"></text>
						</view>
						<view class="option-text">视频号</view>
						<view class="option-desc">微信视频号</view>
					</view>
				</view>

				<view v-if="jiexi_text_show" class="url-input-section">
					<view class="input-header">
						<text>视频地址解析</text>
					</view>
					<textarea maxlength="-1" @input="JieXiInput" :value="jiexi_text" class="url-textarea"
						placeholder="请输入视频地址"></textarea>
					<view class="parse-btn-container">
						<button @tap="jiexi_url" class="parse-btn">
							<text class="_icon-search"></text>
							<text>解析视频</text>
						</button>
					</view>
				</view>

				<view v-if="shipinhao_show" class="shipinhao-section">
					<view class="shipinhao-header">
						<text>视频号设置</text>
					</view>
					<view class="cu-form-group">
						<view class="title">feed-token</view>
						<input @input="shipinhaoInput" :value="feedToken" class="modern-input"
							placeholder="请填写feed-token" />
					</view>
					<view class="tip-text"><text class="_icon-info" style="margin-right: 10rpx;"></text> 暂不支持纯图片视频号内容</view>
					<view class="preview-btn-container">
						<button @tap="shipinhao_show_yl_do" class="preview-btn">
							<text>预览</text>
						</button>
					</view>
					<view v-if="shipinhao_show_yl" class="video-preview-container">
						<channel-video :feed-token="feedToken"></channel-video>
					</view>
				</view>

				<view v-if="jiexi_text_show" class="remark-section">
					<text class="remark-emoji _icon-info"></text>
					<text class="remark-text">{{ $state.copyright.app_remark }}</text>
				</view>
			</view>
		</view>

		<view style="padding:0px 10px;">
			<block v-if="Jiugongge">
				<form>
					<view class="bg-white margin-top-xs" style="border-radius: 20rx;overflow: hidden;;">
						<view style="background-color:#fff;margin-top:5px;">
							<view class="cu-form-group" v-if="version == 0">
								<view @tap="hide_hlep" class="title" style="font-size:13px;">请写点什么吧</view>
							</view>
						</view>
						<textarea v-if="get_hidden" :value="get_content[0].value"
							style="border-radius:3px;width:100%;padding:10px 30px 10px 15px;background-color:#fff;height:150px;"
							class="weui-textarea" maxlength="-1" @input="textareaAInput" placeholder="来吧，畅所欲言..." />
						<view class="flex justify-between align-center bg-white" style="padding: 0px 30rpx 15px;">
							<view>
								<text @tap="openExMode" data-index="0" class="cicon-emoji-o"
									style="font-size: 25px;"></text>
							</view>
						</view>
					</view>
					<view class="cu-bar bg-white margin-top">
						<view class="action">
							图片上传 <text style="font-size:11px;">（长按拖动排序）</text>
						</view>
						<view class="action">
							{{ img_arr.length }}/9
						</view>
					</view>
					<view class="cu-form-group">
						<view class="image-drag-container">
							<movable-area class="movable-area"
								:style="'min-height:' + (imageWitdh) + 'px;height:' + (areaHeight) + 'px'">
								<view class="image-choose-container">
									<view class="image-item"
										:style="'width:' + (imageWitdh) + 'px;height:' + (imageWitdh) + 'px'"
										:data-url="url" :data-index="index" @longpress="handleLongTap"
										@touchend="handleTouchEnd" @touchmove.stop.prevent="handleTouchMove"
										v-for="(url, index) in (img_arr)" :key="index">
										<image :src="url" mode="aspectFill" @tap.stop.prevent="handlePreview"
											:data-index="index" :data-url="url"></image>
										<view class="close" @tap.stop.prevent="handleDelete" :data-index="index"
											style="z-index: 100;"><text class="_icon-close"></text>
										</view>
									</view>
									<view class="add-button"
										:style="'width:' + (imageWitdh) + 'px;height:' + (imageWitdh) + 'px'"
										v-if="img_arr.length >= 0 && img_arr.length < 9"
										@tap.stop.prevent="handleChooseImage"><text class="_icon-add"></text></view>
									<view :style="'width:' + (imageWitdh) + 'px'" class="image-item image-item-temp"
										v-if="img_arr.length % 3 == 1">
									</view>
								</view>
								<movable-view class="movable-view"
									:style="'z-index: 10;width:' + (imageWitdh) + 'px;height:' + (imageWitdh) + 'px'"
									v-if="!hidden" :x="x - 80" :y="y - 50" direction="all" :damping="5000"
									:friction="1">
									<image :src="currentImg" v-if="currentImg.length > 0"></image>
								</movable-view>
							</movable-area>
						</view>
					</view>
				</form>
			</block>
			<view v-if="!Jiugongge" style="border-radius:3px;position:relative;overflow:hidden;"
				v-for="(item, content_index) in (get_content)" :key="content_index">
				<view style="background-color:#fff;margin-top:5px;">
					<view class="cu-form-group" v-if="version == 0">
						<view class="title" style="font-size:13px;">设置为隐藏域
							<!-- <text class="cuIcon-question lg text-gray" style="margin-left: 5px;"></text> -->
						</view>
						<switch @change="get_switch" class="red" :data-key="content_index"></switch>
					</view>
				</view>
				<textarea v-if="get_hidden && item.type == 'text'" :value="item.value" :data-key="content_index"
					style="border-radius:3px;width:100%;padding:10px;background-color:#fff;height:150px;"
					:focus="my_focus" class="weui-textarea" maxlength="-1" @input="get_text_len"
					placeholder="来吧，畅所欲言..." />

				<view v-if="item.type == 'img'"
					style="padding:10px;background-color:#fff;margin-top:5px;text-align:center;">
					<image :src="item.value" mode="widthFix" style="width:200px;"></image>
				</view>
				<view class="flex justify-between align-center bg-white" style="padding: 0px 30px 15px;">
					<view>
						<text v-if="item.type == 'text'" @tap="openExMode" :data-index="content_index"
							class="cicon-emoji-o" style="font-size: 25px;"></text>
					</view>
					<view v-if="get_hidden && hide_hlep_mod == false" @tap="del_add_type" :data-key="content_index">
						<view class="cicon-close-round-o text-red" style="font-size: 25px;"></view>
					</view>
				</view>
			</view>
			<view v-if="!Jiugongge" class="flex padding-sm justify-center bg-white solid-bottom"
				style="margin:5px 0px;border-radius: 3px;">
				<block>
					<view @tap="add_type" style="text-align:center;line-height:40px;padding: 0px 50rpx;">
						<!-- <text class="cicon-input-o text-black _add_iocn_size"></text> -->
						<text class="_add_text_size"><text class="cicon-input" style="margin-right: 10rpx;"></text>文字</text>
					</view>
					<view @tap="chooseImage" style="text-align:center;line-height:40px;padding: 0px 50rpx;">
						<!-- <text class="cicon-pic text-black _add_iocn_size"></text> -->
						<text class="_add_text_size"><text class="cicon-pic" style="margin-right: 10rpx;"></text> 图片</text>
					</view>
					<view v-if="tuya_mod" @tap="set_tuya"
						style="text-align:center;line-height:40px;padding: 0px 50rpx;">
						<!-- <text class="cicon-palette text-black _add_iocn_size"></text> -->
						<text class="_add_text_size"><text class="cicon-paint" style="margin-right: 10rpx;"></text>涂鸦</text>
					</view>
				</block>
			</view>
			<!-- 投票 -->
			<view style="background-color:#ffffff;padding-bottom: 20rpx;border-radius: 8px;margin: 5px 0;"
				v-if="fa_type == 6">
				<!-- 投票设置标题 -->
				<view class="action"
					style="padding:15px 10px;font-weight: 500;font-size:16px;border-bottom: 1px solid #f0f0f0;">
					<text style="color: #333;">设置投票内容</text>
				</view>

				<!-- 投票选项列表 -->
				<view style="padding: 10px 0;">
					<view class="cu-form-group" v-for="(item, o_index) in option" :key="o_index"
						style="margin: 8px 0;">
						<view class="title" style="font-weight:400;display: flex;align-items: center;">
							<text style="color: #555;">选项{{ o_index + 1 }}</text>
						</view>
						<input @input="input_advantage" :data-index="o_index" placeholder="请输入投票选项名称"
							style="border: 1px solid #e0e0e0;border-radius: 4px;padding: 8px 12px;height:70rpx" />
					</view>
				</view>

				<!-- 截止时间设置 -->
				<view
					style="display: flex;justify-content: space-between;background-color: #fff;padding: 10px 30rpx;border-top: 1px solid #f0f0f0;border-bottom: 1px solid #f0f0f0;">
					<view style="display: flex;align-items: center;margin-bottom: 8px;">
						<text style="color: #555;font-weight: 400;">截止时间</text>
					</view>
					<view>
						<pickerYMDHM data-key="2" @onPickerChange="onPickerChange($event, { key: '2' })"
							startDate="2025-01-01" endDate="2099-01-01">
						</pickerYMDHM>
					</view>
				</view>

				<!-- 多选设置 -->
				<view class="cu-form-group" style="padding: 15px 30rpx;">
					<view class="title" style="font-weight:400;display: flex;align-items: center;">
						<text style="color: #555;">是否为多选</text>
					</view>
					<switch class="red sm" @change="onChange_choice" :checked="option_choice"></switch>
				</view>

				<!-- 操作按钮 -->
				<view style="text-align:center;margin-top:15px;padding: 0 10px;">
					<button @tap="click_advantage" class="cu-btn bg-green"
						style="border-radius: 6px;margin-right: 10px;">
						<text style="font-size: 16px;margin-right: 4px;" class="_icon-add"></text>
						<text style="font-weight:400;">添加选项</text>
					</button>
					<button @tap="click_advantage_del" class="cu-btn bg-orange" style="border-radius: 6px;">
						<text style="font-size: 16px;margin-right: 4px;" class="_icon-delete"></text>
						<text style="font-weight:400;">删除选项</text>
					</button>
				</view>

			</view>
			<!-- 活动内容区域 -->
			<view class="activity-card" v-if="fa_type == 4">
				<view class="activity-header">
					<text class="section-title">活动设置</text>
				</view>

				<!-- 活动地址 - 左右布局 -->
				<view class="activity-item" @tap="get_position"
					style="display: flex;justify-content: space-between;align-items: center;padding: 15px 20px;border-bottom: 1px solid #f0f0f0;">
					<view class="activity-label" style="display: flex;align-items: center;flex: 0 0 auto;">
						<text class="activity-emoji cicon-pin-drop" style="margin-right: 8px;"></text>
						<text style="color: #333;font-size: 14px;">活动地址</text>
					</view>
					<text class="activity-value"
						style="flex: 1;text-align: right;color: #666;font-size: 14px;margin-left: 20px;">{{ active_address == '' ? '点击选择地址' : active_address }}</text>
				</view>

				<!-- 开始时间 - 左右布局 -->
				<view class="activity-item"
					style="display: flex;justify-content: space-between;align-items: center;padding: 15px 20px;border-bottom: 1px solid #f0f0f0;">
					<view class="activity-label" style="display: flex;align-items: center;flex: 0 0 auto;">
						<text class="activity-emoji cicon-time" style="margin-right: 8px;"></text>
						<text style="color: #333;font-size: 14px;">开始时间</text>
					</view>
					<view style="flex: 1;text-align: right;margin-left: 20px;">
						<pickerYMDHM data-key="1" :date="active_star_time"
							@onPickerChange="onPickerChange($event, { key: '1' })" startDate="2025-01-01"
							endDate="2099-01-01">
						</pickerYMDHM>
					</view>
				</view>

				<!-- 结束时间 - 左右布局 -->
				<view class="activity-item"
					style="display: flex;justify-content: space-between;align-items: center;padding: 15px 20px;border-bottom: 1px solid #f0f0f0;">
					<view class="activity-label" style="display: flex;align-items: center;flex: 0 0 auto;">
						<text class="activity-emoji cicon-time" style="margin-right: 8px;"></text>
						<text style="color: #333;font-size: 14px;">结束时间</text>
					</view>
					<view style="flex: 1;text-align: right;margin-left: 20px;">
						<pickerYMDHM data-key="2" :date="active_end_time"
							@onPickerChange="onPickerChange($event, { key: '2' })" startDate="2025-01-01"
							endDate="2099-01-01">
						</pickerYMDHM>
					</view>
				</view>

				<!-- 活动人数 - 左右布局 -->
				<view class="activity-item"
					style="display: flex;justify-content: space-between;align-items: center;padding: 15px 20px;border-bottom: 1px solid #f0f0f0;">
					<view class="activity-label" style="display: flex;align-items: center;flex: 0 0 auto;">
						<text class="activity-emoji cicon-person-add" style="margin-right: 8px;"></text>
						<text style="color: #333;font-size: 14px;">活动人数</text>
					</view>
					<view style="flex: 1;text-align: right;margin-left: 20px;">
						<input type="number" @input="active_set_num" maxlength="7" :value="active_num"
							class="modern-input" placeholder="请输入活动人数"
							style="text-align: right;border: none;background: transparent;color: #666;font-size: 14px;padding: 0;" />
					</view>
				</view>

				<view class="activity-tip"
					style="padding: 15px 20px;background-color: #f8f9fa;border-radius: 0 0 8px 8px;">
					<text class="tip-emoji _icon-info" style="margin-right: 6px;"></text>
					<text class="tip-text" style="color: #666;font-size: 12px;">收费活动请设置为订阅帖</text>
				</view>
			</view>
			<!-- 功能按钮区域 -->
			<view class="function-buttons-card">
				<view class="function-header">
					<text class="section-title">发布设置</text>
				</view>
				<view class="function-grid">
					<view v-if="copyright.rel_paper_location_hide == 0" @tap="get_this_button" data-k="1"
						:class="'function-item ' + (button_key == 1 ? 'active' : '')">
						<text class="function-emoji cicon-location-on"></text>
						<view class="function-text">位置</view>
					</view>
					<view v-if="version == 0 && copyright.welfare_arbor == 1" @tap="get_this_button" data-k="2"
						:class="'function-item ' + (button_key == 2 ? 'active' : '')">
						<text class="function-emoji cicon-moneybag"></text>
						<view class="function-text">福袋</view>
					</view>
					<block v-if="copyright.buy_paper_arbor == 1 && version == 0">
						<block v-if="copyright.buy_paper_member == 0">
							<view @tap="get_this_button" data-k="3"
								:class="'function-item ' + (button_key == 3 ? 'active' : '')">
								<text class="function-emoji cicon-magic"></text>
								<view class="function-text">订阅</view>
							</view>
						</block>
						<block v-if="copyright.buy_paper_member == 1 && is_vip == 1">
							<view @tap="get_this_button" data-k="3"
								:class="'function-item ' + (button_key == 3 ? 'active' : '')">
								<text class="function-emoji cicon-magic"></text>
								<view class="function-text">订阅</view>
							</view>
						</block>
					</block>
					<view v-if="copyright.reprint_arbor == 1" @tap="get_this_button" data-k="4"
						:class="'function-item ' + (button_key == 4 ? 'active' : '')">
						<text class="function-emoji cicon-reply-all"></text>
						<view class="function-text">禁转</view>
					</view>
					<view v-if="copyright.rel_paper_topicsd_hide == 0" @tap="get_this_button" data-k="5"
						:class="'function-item ' + (button_key == 5 ? 'active' : '')">
						<text class="function-emoji cicon-slack-square"></text>
						<view class="function-text">话题</view>
					</view>
					<view class="function-item" @tap="get_jiu" v-if="copyright.rel_paper_image_hide == 0">
						<text class="function-emoji cicon-pic"></text>
						<view class="function-text">{{ Jiugongge ? '图文' : '九宫格' }}</view>
					</view>
					<view class="function-item" v-if="open_user_phone && version == 0">
						<UserPhone v-if="open_user_phone" Type="1" @SetPhone="set_phone" style="width: 100%;">
						</UserPhone>
					</view>
					<view v-if="open_user_wangpan && version == 0 && copyright.rel_paper_icon_hide == 0"
						class="function-item" @tap="open_file_d">
						<text class="function-emoji cicon-folder"></text>
						<view class="function-text">附件</view>
					</view>
				</view>
			</view>
			<UserPhone class="bg-white" v-if="open_user_phone_input && open_user_phone" @Phone="get_phone" Type="2"
				style="width: 100%;"></UserPhone>
			<view v-if="file_id != 0">
				<view class="flex justify-between align-center bg-white">
					<view class="padding-sm margin-xs">
						<view class="flex align-center">
							<view class="margin-xs">
								<image
									:src="(http_root) + 'addons/yl_welore/web/static/file_icon/' + (file_forw_info.file_icon)"
									style="height: 30px;width: 30px;"></image>
							</view>
							<view class="margin-xs" style="margin-left: 15px;">
								<view class="font-yl-2 text_num_1" style="font-size: 14px;font-weight: 600;">
									{{ file_forw_info.file_name }}
								</view>
								<view style="font-size: 12px;margin-top: 8px;color: #9E9E9E;">
									<text>{{ file_forw_info.add_time }}</text>
									<text v-if="file_forw_info.is_dir == 0" style="margin-left: 20px;">{{ file_forw_info.file_size
                  }}</text>
									<text v-if="file_forw_info.is_dir == 1" style="margin-left: 20px;">共{{ file_forw_info.file_count
                  }}个文件</text>
								</view>
							</view>
						</view>
					</view>
					<view @tap="cancel_file" style="padding-right: 40rpx;">
						<view class="cu-tag radius">取消</view>
					</view>
				</view>
				<view class="cu-form-group" style="padding: 1rpx 40rpx;">
					<view class="title text_lin font-yl-2">文件禁止分享</view>
					<switch @change="file_off_money_change" data-key="1" class="red sm" :checked="file_off_fowd">
					</switch>
				</view>
				<view class="cu-form-group" style="padding: 1rpx 40rpx;">
					<view class="title text_lin font-yl-2">设置文件售价</view>
					<switch @change="file_off_money_change" data-key="2" class="red sm" :checked="file_off_money">
					</switch>
				</view>
			</view>
			<view v-if="gambit_name" class="flex justify-between align-center bg-white padding-sm"
				style="margin-top: 5px;">
				<view style="vertical-align: middle;">话题：{{ gambit_name }}</view>
				<view class="cuIcon-roundclosefill text-gray" @tap="close_button" data-key="5" style="font-size: 25px;">
				</view>
			</view>
			<view v-if="is_open" class="flex justify-between align-center bg-white padding-sm" style="margin-top: 5px;">
				<view style="vertical-align: middle;">已设置禁止转发</view>
				<view class="cuIcon-roundclosefill text-gray" @tap="close_button" data-key="4" style="font-size: 25px;">
				</view>
			</view>
			<view v-if="fuli_is" class="flex justify-between align-center bg-white padding-sm" style="margin-top: 5px;">
				<view style="vertical-align: middle;">已设置福袋帖子</view>
				<view class="cuIcon-roundclosefill text-gray" @tap="close_button" data-key="2" style="font-size: 25px;">
				</view>
			</view>
			<view v-if="dingyue_is" class="flex justify-between align-center bg-white padding-sm"
				style="margin-top: 5px;">
				<view style="vertical-align: middle;">已设置订阅帖子 金额{{ xian_money }}</view>
				<view class="cuIcon-roundclosefill text-gray" @tap="close_button" data-key="3" style="font-size: 25px;">
				</view>
			</view>
			<view v-if="is_position" class="flex justify-between align-center bg-white padding-sm"
				style="margin-top: 5px;">
				<view style="vertical-align: middle;">{{ ppooss }}</view>
				<view class="cuIcon-roundclosefill text-gray" @tap="close_button" data-key="1" style="font-size: 25px;">
				</view>
			</view>
			<view v-if="copyright.reprint_arbor && button_key == 4"
				class="flex justify-between align-center bg-white padding-sm" style="margin-top: 5px;">
				<view style="vertical-align:middle;">禁止转发</view>
				<switch @change="onChange" class="orange  sm" :checked="is_open"></switch>
			</view>

			<view v-if="red_paper && button_key == 2" style="background-color:#fff;position: relative;margin-top: 5px;">
				<text class="bg-gray" @tap="close_button" data-key="2"
					style="bottom: 15px;left: 10px;position: absolute;vertical-align: middle;border-radius: 5px;font-size: 14px;padding: 5px 10px;">取消</text>
				<text @tap="add_button" data-key="2"
					style="bottom: 15px;right: 10px;position: absolute;vertical-align: middle;color: #ffffff;background-color: #00CC66;border-radius: 5px;font-size: 14px;padding: 5px 10px;">确定</text>
				<view v-if="FuDaiType == 0" style="padding:12px 15px;font-size:12px;">我的{{ design.currency }}数量：{{
          user_info.conch }}
				</view>
				<view v-if="FuDaiType == 1" style="padding:12px 15px;font-size:12px;">我的{{ design.confer }}数量：{{
          user_info.fraction }}
				</view>
				<view class="cu-form-group margin-top" v-if="red_type == 1"
					style="border: 1px solid #9999;margin: 0px 20px;border-radius: 3px;min-height: 40px;">
					<view class="title">总数量</view>
					<input @input="get_red_money" :value="xian_red_money" type="digit" maxlength="7" right title="总数量"
						mode="wrapped" placeholder="填写数量" />
				</view>
				<view class="cu-form-group margin-top" v-if="red_type == 0"
					style="border: 1px solid #9999;margin: 0px 20px;border-radius: 3px;min-height: 40px;">
					<view class="title">单个数量</view>
					<input @input="get_red_money_d" :value="xian_red_money" type="digit" maxlength="7" right title="总数量"
						mode="wrapped" placeholder="填写单个数量" />
				</view>

				<view @tap="set_red_type" style="padding:10px;font-size:14px;">
					当前为
					<text v-if="red_type == 1">拼手气</text>
					<text v-if="red_type == 0">普通</text>玩法，改为
					<text v-if="red_type == 1" style="color:#BD9957">普通玩法</text>
					<text v-if="red_type == 0" style="color:#BD9957">拼手气玩法</text>
				</view>
				<view class="cu-form-group margin-top"
					style="border: 1px solid #9999;margin: 0px 20px;border-radius: 3px;min-height: 40px;">
					<view class="title">福袋个数</view>
					<input @input="get_red_count" :value="zong_red_count" type="number" maxlength="7" right title="总金额"
						mode="wrapped" placeholder="填写个数" />
				</view>
				<view style="text-align:center;font-size:30px;font-weight:700;padding: 10px 0px;">

					<picker @change="PickerFuDai" :value="index" :range="DingYueMoneyType">
						<view style="text-align:center;font-size:30px;font-weight:700;padding: 10px;">
							<text>{{ zong_red_money }}</text>
							<text>{{ FuDaiType == 0 ? design.currency : design.confer }}</text>
							<text class="cuIcon-triangledownfill lg text-gray"></text>
						</view>
					</picker>
				</view>
			</view>

			<view v-if="money_paper && button_key == 3"
				style="background-color:#fff;padding-top: 10px;position: relative;margin-top: 5px;">

				<picker @change="PickerDingYue" :value="index" :range="DingYueMoneyType">
					<view style="text-align:center;font-size:30px;font-weight:700;padding: 10px;">
						<text>{{ xian_money }}</text>
						<text>{{ DingYueType == 0 ? design.currency : design.confer }}</text>
						<text class="cuIcon-triangledownfill lg text-gray"></text>
					</view>
				</picker>
				<view class="cu-form-group margin-top"
					style="border: 1px solid #9999;margin: 0px 150rpx;border-radius: 3px;min-height: 40px;">
					<view class="title">售价</view>
					<input @input="get_paper_money" :value="money" type="digit" maxlength="7" right title="总数量"
						mode="wrapped" :placeholder="'售价' + (DingYueType == 0 ? design.currency : design.confer)" />
				</view>
				<view class="flex solid-bottom padding justify-center">
					<view @tap="get_this_button" data-k="3" class="bg-grey padding-sm margin-xs radius"
						style="text-align: center;width: 30%;"><text class="_icon-close"></text> 取消</view>
					<view @tap="add_button" data-key="3" class="bg-green padding-sm margin-xs radius"
						style="text-align: center;width: 30%;"><text class="_icon-check"></text> 确定</view>
				</view>
			</view>

			<view v-if="copyright.engrave_arbor == 1 && version == 0" class="flex justify-center bg-white align-center"
				style="border-radius: 24rpx;margin:5px 0px;">
				<view class="padding-sm margin-sm" v-if="top_info == ''">
					<text @tap="to_unlock" class="cu-tag line-cyan radius">自选昵称</text>
				</view>
				<view class="padding-sm margin-sm" @tap="to_unlock" v-if="top_info != ''">
					<image style="width: 30px;height: 30px;vertical-align: middle;border-radius:50%;"
						:src="top_info.forgery_head">
					</image>
					<text
						style="font-weight: 300;font-size:12px;color:#000000;margin:0px 10px;vertical-align: middle;">{{
            top_info.forgery_name }}</text>
					<text @tap.stop.prevent="cancel_unlock" class="cu-tag line-black radius"
						style="font-weight: 300;">取消</text>
				</view>
			</view>
			<button :disabled="is_submit" :loading="is_submit" @tap="submit" class="cu-btn block publish-btn">
				<text v-if="!is_submit">发布内容</text>
				<text v-if="is_submit">发布中...</text>
			</button>

			<navigator url="/yl_welore/pages/packageC/post_notice/index" hover-class="none">
				<view class="post-notice">
					<view class="notice-content">
						<text>发帖须知</text>
						<text class="cuIcon-question lg"></text>
					</view>
				</view>
			</navigator>
		</view>
		<view :class="'cu-modal ' + (hide_hlep_mod ? 'show' : '')">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">提示</view>
					<view class="action" @tap="handleClose">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding">
					可设置单独文本/图片隐藏。
				</view>
				<view class="cu-bar bg-white justify-end">
					<view class="action">
						<button class="cu-btn bg-green margin-left" @tap="handleClose">确定</button>
					</view>
				</view>
			</view>
		</view>
		<view :class="'cu-modal ' + (visible2 ? 'show' : '')">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">提示</view>
					<view class="action" @tap="handleClose">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding">
					发布需要使用您的录音功能，是否允许？
				</view>
				<view class="cu-bar bg-white justify-end">
					<view class="action">
						<button class="cu-btn line-green text-green" @tap="handleClose">取消</button>
						<button class="cu-btn bg-green margin-left" @tap="handleOk">确定</button>

					</view>
				</view>
			</view>
		</view>
		<view :class="'cu-modal ' + (visible_pos ? 'show' : '')">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">提示</view>
					<view class="action" @tap="handleClose">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding">
					需要使用您的地理位置，是否允许？
				</view>
				<view class="cu-bar bg-white justify-end">
					<view class="action">
						<button class="cu-btn line-green text-green" @tap="handleClose">取消</button>
						<button class="cu-btn bg-green margin-left" @tap="handleOk">确定</button>

					</view>
				</view>
			</view>
		</view>


		<view :class="'cu-modal color-picker-modal ' + (text_color ? 'show' : '')">
			<view class="cu-dialog color-picker-dialog">
				<view class="color-picker-header">
					<view class="header-content">
						<text class="header-icon">🎨</text>
						<text class="header-title">选择字体颜色</text>
					</view>
					<view class="close-btn" @tap="hideModal">
						<text class="cuIcon-close"></text>
					</view>
				</view>
				<view class="color-picker-body">
					<view class="color-grid">
						<view @tap="handleClick4" :data-index="a_index"
							class="color-option"
							:style="'background-color:' + (item.color) + ';'"
							v-for="(item, a_index) in (actions4)" :key="a_index">
							<view class="color-overlay"></view>
						</view>
					</view>
				</view>
				<view class="color-picker-footer">
					<button class="cancel-btn" @tap="hideModal">
						<text>取消</text>
					</button>
				</view>
			</view>
		</view>


		<view :class="'DrawerClose ' + (showLeft ? 'show' : '')" @tap="toggleLeft">
			<text class="cuIcon-pullright"></text>
		</view>
		<scroll-view scroll-y @scrolltolower="lower" :class="'DrawerWindow ' + (showLeft ? 'show' : '')">
			<view style="border-radius:0px;" class="cu-list menu card-menu margin-top-xl margin-bottom-xl shadow-lg">
				<view @tap="open_all" class="cu-item arrow">
					<view class="content">
						<text class="cuIcon-discoverfill text-orange"></text>
						<text class="text-grey">更多{{ design.landgrave }}</text>
					</view>
				</view>
				<view class="cu-item arrow" v-for="(item, rightIndex) in (navRightItems)" :key="rightIndex">




					<view class="content">
						<view @tap="select_qq_id" :data-index="rightIndex" :data-id="item.id"
							:data-name="item.realm_name" class="weui-cell" style="padding:0;margin:10px 0 25px 0;">

							<view class="cu-avatar round lg"
								:style="'background-image:url(' + (item.realm_icon) + ');'">
								<view v-if="item.attention == 1" class="cu-tag badge cuIcon-lock bg-red"></view>
							</view>
							<view style="margin-left: 10px;">
								<view style="font-size:28rpx;color:#000000;">

									<view class="course-name">
										{{ item.realm_name }}
									</view>
								</view>
								<view style="font-size: 10px;color: #888888;">
									<text>发帖等级</text>
									<text style="margin-left: 5px;">Lv.{{ item.release_level }}</text>
								</view>
								<view style="font-size: 10px;color: #888888;">
									<text>发帖次数(每日)</text>
									<text v-if="item.release_count != 0"
										style="margin-left: 5px;">{{ item.release_count }}　篇</text>
									<text v-if="item.release_count == 0" style="margin-left: 5px;">无限制</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<view :class="'cu-modal ' + (show_msg ? 'show' : '')">
			<!-- <view class="cu-modal show"> -->
			<view class="cu-dialog"
				style="position: absolute;top: 20%;left: 0px;right: 0px;background-color: transparent;">
				<view style="font-size:18px;color:#fff;">打开提醒，最新消息不再错过</view>
				<view style="font-size:16px;color:#fff;height:40px;margin-top: 20px;">
					<text style="vertical-align: middle;margin-right: 5px;">勾选底部</text>
					<text
						style="color: #000;background-color: #fff;border-radius: 20px;padding: 5px 10px;font-size: 14px;"><text
							style="font-size:18px;vertical-align: middle;" class="cuIcon-roundcheck text-green"></text>
						<text style="vertical-align: middle;">总是保持以上选择</text></text>
				</view>
			</view>
		</view>
		<!-- 选择文件 -->
		<view :class="'cu-modal bottom-modal ' + (file_mod ? 'show' : '')">
			<view class="cu-dialog"
				style="height: 80%; border-top-left-radius: 10px;    border-top-right-radius: 10px;">
				<view class="cu-bar bg-white solid-bottom">
					<view class="action text-white">选择网盘文件</view>
					<view class="action text-black" @tap="hideFileMod">取消</view>
				</view>
				<scroll-view :scroll-y="true" style="height: 90%;" @scrolltolower="scroll_file_list">
					<view v-if="is_dir == 1" class="cu-bar bg-white" @tap="back_file_list">
						<view class="action">
							<text class="cuIcon-back text-white"></text> 返回
						</view>
					</view>
					<view class="flex justify-between align-center bg-white"
						v-for="(item, file_index) in (flie_net_list)" :key="file_index">
						<view class="padding-sm margin-xs">
							<view class="flex align-center">
								<view class="margin-xs">
									<image
										:src="(http_root) + 'addons/yl_welore/web/static/file_icon/' + (item.file_icon)"
										style="height: 30px;width: 30px;"></image>
								</view>
								<view class="margin-xs" style="margin-left: 10rpx;">
									<view class="font-yl-2 text_num_1"
										style="font-size: 14px;font-weight: 600;text-align: left;">
										<text>{{ item.file_name }}</text>
										<text v-if="item.is_dir == 0">.{{ item.file_suffix }}</text>
									</view>
									<view style="font-size: 12px;margin-top: 10rpx;color: #9E9E9E;text-align: left;">
										<text>{{ item.add_time }}</text>
										<text v-if="item.is_dir == 0"
											style="margin-left: 20px;">{{ item.file_size }}</text>
									</view>
								</view>
							</view>
						</view>
						<view class="padding-sm margin-xs">
							<button @tap="choice_file" :data-index="file_index" data-key="1" v-if="item.is_dir == 1"
								class="cu-btn margin-right">打开</button>
							<button @tap="choice_file" :data-index="file_index" data-key="2" class="cu-btn"
								style="width: 120rpx;">选择</button>
						</view>
					</view>
					<view style="margin:20px 0px 100px 0px" :class="'cu-load ' + (!file_di_msg ? 'loading' : 'over')">
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 小表情 -->
		<view :class="'cu-modal bottom-modal ' + (ExModal ? 'show' : '')" @tap="hideModal">
			<view class="cu-dialog" style="padding-bottom: 20px;background-color: #F3F3F3;">
				<view style="width: 100%;height: 400rpx;background-color: #F3F3F3;">
					<swiper :indicator-dots="true" style="height:400rpx;">
						<block v-for="(emojis, t_index) in (expression)" :key="t_index">
							<swiper-item>
								<view class="grid col-5 text-center"
									style="padding-left: 7px;padding-top: 10px;margin-bottom: 60rpx;">
									<view @tap.stop.prevent="set_emoji" :data-key="t_index" :data-index="n_index"
										style="width: 60rpx;height: 60rpx;margin: 5px;"
										v-for="(n_item, n_index) in (emojis)" :key="n_index">
										<image :src="(http_root) + 'addons/yl_welore/web/static/expression/' + (n_item)"
											style="width: 60rpx;height: 60rpx;"></image>
									</view>
								</view>
							</swiper-item>
						</block>
					</swiper>
				</view>
			</view>
		</view>
		<!-- 选择文件 -->
		<view v-if="loadModal"
			style="position: fixed;top: 0;height: 100%;width: 100%;z-index: 10000;background-color: rgba(0, 0, 0, 0.555);">
		</view>
		<view v-if="loadModal" class="padding bg-white "
			style="position: fixed;bottom: 50%;z-index: 10001; width: 90%;border-radius: 10px;margin: 0 auto;left: 0;right: 0;box-shadow: 0 4px 20px rgba(0,0,0,0.15);">

			<!-- 模态框标题 -->
			<view style="text-align: center;padding: 10px 0;border-bottom: 1px solid #f0f0f0;margin-bottom: 15px;">
				<text style="font-size: 18px;font-weight: 600;color: #333;">内容上传中</text>
			</view>

			<!-- 视频上传区域 -->
			<view style="margin-bottom: 20px;">
				<view style="display: flex;align-items: center;margin-bottom: 8px;">
					<text style="font-size: 14px;color: #333;">视频上传中</text>
					<text v-if="VideoUploadSuccess" style="margin-left: auto;color: #4CAF50;" class="_icon-check"></text>
				</view>
				<view class="flex" style="align-items: center;">
					<view class="cu-progress round striped active" style="flex: 1;">
						<view class="bg-green" :style="'width:' + (VideoTask) + '%;'"></view>
					</view>
					<text style="margin-left: 10px;font-size: 12px;color: #999;min-width: 35px;">{{ VideoTask }}%</text>
					<text v-if="VideoTask == 100 && !VideoUploadSuccess"
						class="cu-load load-icon loading margin-left-sm"></text>
				</view>
			</view>

			<!-- 封面图上传区域 -->
			<view>
				<view style="display: flex;align-items: center;margin-bottom: 8px;">
					<text style="font-size: 14px;color: #333;">封面图上传中</text>
					<text v-if="ImgUploadSuccess" style="margin-left: auto;color: #4CAF50;" class="_icon-check"></text>
				</view>
				<view class="flex" style="align-items: center;">
					<view class="cu-progress round striped active" style="flex: 1;">
						<view class="bg-green" :style="'width:' + (ImgTask) + '%;'"></view>
					</view>
					<text style="margin-left: 10px;font-size: 12px;color: #999;min-width: 35px;">{{ ImgTask }}%</text>
					<text v-if="ImgTask == 100 && !ImgUploadSuccess"
						class="cu-load load-icon loading margin-left-sm"></text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	const recorderManager = uni.getRecorderManager();
	const innerAudioContext = uni.createInnerAudioContext();
	innerAudioContext.obeyMuteSwitch = false;
	var http = require("@/yl_welore/util/http.js");
	import util from '@/yl_welore/util/data.js';
	import pickerYMDHM from "@/yl_welore/util/pickerYMDHM/pickerYMDHM";
	import UserPhone from "@/yl_welore/yl_plugin/dial/index";
	const options = {
		duration: 300000, //指定录音的时长，单位 ms
		sampleRate: 16000, //采样率
		numberOfChannels: 1, //录音通道数
		encodeBitRate: 96000, //编码码率
		format: 'mp3', //音频格式，有效值 aac/mp3
		frameSize: 50, //指定帧大小，单位 KB
	}
	var recordTimeInterval = "";
	var isButtonDown = false;
	var canvasw = 0;
	var canvash = 0;
	uni.getSystemInfo({
		success: (res) => {
			canvasw = res.windowWidth; //设备宽度
			canvash = res.windowHeight;
		}
	});
	export default {
		components: {
			UserPhone,
			pickerYMDHM
		},
		/**
		 * 页面的初始数据
		 */
		data() {
			return {
				loadModal: false,
				no_title: '',
				title: '',
				design: {},
				shipinhao_show: false,
				shipinhao_show_yl: false,
				feedToken: '',
				imageWitdh: 0,
				tuya:false,
				x: 0, // movable-view的坐标
				y: 0,
				areaHeight: 0, // movable-area的高度
				hidden: true, // movable-view是否隐藏
				currentImg: '', // movable-view的图片地址
				currentIndex: 0, // 要改变顺序的图片的下标
				pointsArr: [], // 每张图片的坐标
				flag: true, // 是否是长按
				scrollTop: 0, // 滚动条距离顶部的距离
				top_info: '',
				http_root: app.globalData.http_root,
				address_latitude: '',
				address_longitude: '',
				is_position: false, //获取我的位置
				position: '', //位置详情
				position_name: '', //位置简介
				is_open: false, //是否开启转发
				fa_type: 0, //0:文字1：语音2：视频
				is_submit: false, //是否正在提交
				visible2: false,
				text_color: false,
				get_hidden: true,
				cccc: '#000000',
				ppooss: '',
				color_list: [{
					color: '#203141',
				}, {
					color: '#23A7F2',
				}, {
					color: '#27A75C',
				}, {
					color: '#F7D66B',
				}, {
					color: '#F12713',
				}, {
					color: '#9B13B5',
				}, {
					color: '#67809F',
				}, {
					color: '#37D6B6',
				}, {
					color: '#F76A0E',
				}, {
					color: '#F8255A',
				}],
				actions4: [{
						color: '#2ae0c8'
					}, {
						color: '#a2e1d4'
					}, {
						color: '#acf6ef'
					}, {
						color: '#cbf5fb'
					}, {
						color: '#bdf3d4'
					}, {
						color: '#e6e2c3'
					}, {
						color: '#e3c887'
					}, {
						color: '#fad8be'
					}, {
						color: '#fbb8ac'
					}, {
						color: '#fe6673'
					}, {
						color: '#D24D57'
					}, {
						color: '#EB7347'
					},
					{
						color: '#FC9D99'
					},
					{
						color: '#26A65B'
					},
					{
						color: '#AEDD81'
					},
					{
						color: '#84AF9B'
					},
					{
						color: '#00CCFF'
					},
					{
						color: '#D0D0D0'
					},
					{
						color: '#2C3E50'
					},
					{
						color: '#000000'
					},

				],
				user_info: {},
				file: '', //文件
				file_forw_info: {},
				top_file: '', //文件
				file_ss: 0, //录音s
				scope_record: true, //是否授权录音
				text: '', //内容
				is_title: false, //是否显示标题
				title_value: "", //标题内容
				title_color: "#333333", //默认颜色
				img_arr: [], //图片集
				top_img_arr: [], //图片集
				img_length: 9, //限制图片数量
				img_botton: true, //图片按钮是否显示
				is_vip: 0,
				check_fa_class: false,
				showLeft: false,
				navLeftItems: [],
				navRightItems: [],
				curNav: -1,
				di_msg: true,
				page: 1,
				version: 1,
				title_arbor: 1,
				//福袋
				red_type: 1,
				zong_red_money: '0.00',
				xian_red_money: '',
				zong_red_count: '',
				FuDaiType: 0, //价格类型
				//订阅
				DingYueMoneyType: ['贝壳', '积分'],
				DingYueType: 0, //价格类型
				red_paper: false,
				money_paper: false,
				money: '',
				xian_money: '0.00',
				get_content: [{
					type: 'text',
					value: '',
					hide: 0
				}],
				Jiugongge: true,
				is_voice: false,
				button_key: 0,
				dingyue_is: false,
				hide_hlep_mod: false,
				fuli_is: false,
				gambit_id: 0,
				gambit_name: '',
				active_address: '',
				active_latitude: '',
				active_longitude: '',
				active_star_time: '',
				active_end_time: '',
				active_num: '',
				show_msg: false,
				vd_width: 0,
				vd_height: 0,
				//投票数据
				option_end_time: 0,
				option_choice: false,
				tuya_mod: false,
				option: [{
					ballot_name: ''
				},{
					ballot_name: ''
				}],
				//电话
				open_user_phone: false,
				my_phone: '',
				open_phone: false,
				open_user_phone_input: false,
				//网盘
				open_user_wangpan: false,
				//解析
				open_jiexi: false,
				file_id: 0,
				file_mod: false,
				flie_net_list: [],
				file_page: 1,
				file_page_key: 1,
				file_page_in: 1,
				is_dir: 0,
				file_off_fowd: false,
				file_off_money: false,
				dir_count: 0, //文件夹数量
				//录音
				tempFilePath: '', //临时文件
				star_recorder: false,
				is_time: 0,
				flie_ss_name: '00:00',
				is_time_name: '00:00',
				show_audio: false,
				//一键拨号
				phone_config: 0,
				//图片上传进度
				ImgTask: 0,
				VideoTask: 0,
				//上传成功状态
				ImgUploadSuccess: false,
				VideoUploadSuccess: false,
				//表情
				expression: [],
				ContentIndex: 0,
				my_focus: false,
				ExModal: false,
				//视频解析
				is_jiexi: false,
				jiexi_text_show: false,
				jiexi_text: '',
				copyright: {}
			}
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {
			uni.hideShareMenu();
			var design = uni.getStorageSync('is_diy');
			var currenTime = util.formatTime(new Date());
			console.log(currenTime);
			var DingYue = [design.currency, design.confer]
			this.DingYueMoneyType = DingYue;
			this.file_id = options.file_id ?options.file_id :0;
			this.copyright = app.globalData.store.getState().copyright;
			this.design = design;
			this.fa_type = options.type;
			this.fa_class = options.fa_class;
			this.no_title = '选择一个' + design.landgrave;
			this.title = '' + options.name;
			this.title_arbor = app.globalData.store.getState().copyright.title_arbor;
			this.gambit_name = options.gambit_name;
			this.gambit_id = options.gambit_id;
			this.active_star_time = currenTime;
			this.active_end_time = currenTime;
			console.log(this.copyright);
			if (this.copyright.hair_graffiti_arbor == 1) {
				this.tuya_mod = true;
			}
			this.get_user_info();
			if (options.type == 2) {
				this.is_title = true;
			}
			if (options.fa_class == 0) {
				this.check_fa_class = true;
			}
			//this.get_left_needle();
			this.get_right_item();
			this.get_diy();
			var subscribe = app.globalData.getCache("subscribe");
			if (!subscribe) {
				app.globalData.subscribe_message((res) => {
					//请求成功的回调函数
					console.log(res);
					if (res == '') {
						return;
					}
					app.globalData.setCache("subscribe", res.parallelism_data);
				}, () => {
					//请求失败的回调函数，不需要时可省略
				})
			}

		},
		onShow() {
			var phone = app.globalData.__PlugUnitScreen('b738745f2187839767e8a5aa93bc336e');
			var wangpan = app.globalData.__PlugUnitScreen('eae4f6cbbecdb89ea5a61ec602ec7000');
			var jiexi = app.globalData.__PlugUnitScreen('c8344a9b5e74dfd193d51d4cbe32aad7');
			this.open_user_phone = phone;
			this.open_user_wangpan = wangpan;
			this.open_jiexi = jiexi;

			var name_card = app.globalData.getCache('name_card');
			var gambit_card = app.globalData.getCache('gambit_card');
			if (name_card) {
				this.top_info = name_card;
				this.get_one_name_card(name_card);
			}
			if (gambit_card) {
				this.gambit_id = gambit_card.gambit_id;
				this.gambit_name = gambit_card.gambit_name,
					this.button_key == gambit_card.button_key;
			}
			if (typeof(this.file_id) != "undefined" && this.file_id != 0) {
				this.get_file_info();
			}
		},

		/**
		 * 生命周期函数--监听页面初次渲染完成
		 */
		onReady() {
			// 计算图片
			this._handleComputedImage();
		},
		// 监听滚动
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		methods: {
			/**
			 * 清除视频
			 */
			clearVideo() {
				this.file = '';
				this.top_img_arr = [];
				this.is_jiexi = false;
				uni.showToast({
					title: '视频已清除',
					icon: 'none',
					duration: 1500
				});
			},
			jiexi_url() {
				uni.showLoading({
					title: '解析中...',
				})
				var b = app.globalData.api_root + 'Scanning/AddAnalysis';
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				params.url = this.jiexi_text;
				http.POST(b, {
					params: params,
					success: (res) => {
						uni.hideLoading();
						console.log(res);
						if (res.data.code == 1) {
							that.title_value = res.data.title;
							that.file = res.data.video;
							that.top_img_arr = res.data.cover;
							that.is_jiexi = true;
						} else {
							uni.showModal({
								title: '提示',
								content: res.data.msg,
								showCancel: false,
								success: (res) => {}
							})
						}
					},
					fail: () => {
						uni.hideLoading();
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: (res) => {}
						})
					},
				})
			},
			JieXiInput(d) {
				var url = d.detail.value;
				this.jiexi_text = url;
			},
			add_jiexi() {
				this.jiexi_text_show = !this.jiexi_text_show;
				this.shipinhao_show=false;
			},
			shipinhao_show_yl_do() {
				this.shipinhao_show_yl = true;
			},
			add_shipinhao() {
				this.jiexi_text_show = false;
				this.shipinhao_show = !this.shipinhao_show;
			},
			shipinhaoInput(e) {
				var value = e.detail.value;
				this.feedToken = value;
			},
			set_emoji(d) {
				var index = d.currentTarget.dataset.index;
				var t_index = d.currentTarget.dataset.key;
				var str = this.expression[t_index][index];
				var k = str.split('.')[0];
				var get_content = this.get_content;
				get_content[this.ContentIndex].value = get_content[this.ContentIndex].value + '[#:' + k + ']'
				console.log(this.get_content);
				this.get_content = get_content;
			},
			openExMode(d) {
				var index = d.currentTarget.dataset.index;
				this.ContentIndex = index;
				this.my_focus = false;
				this.ExModal = true;
			},
			file_off_money_change(d) {
				var key = d.currentTarget.dataset.key;
				if (key == 1) {
					this.file_off_fowd = d.detail.value;
				} else {
					this.money_paper = d.detail.value;
					this.red_paper = false;
					this.button_key = d.detail.value ? 3 : 0;
					this.dingyue_is = false;
					this.file_off_money = d.detail.value;
				}

			},
			cancel_file() {
				this.file_id = 0;
				this.file_mod = '';
			},
			PickerDingYue(d) {
				this.DingYueType = d.detail.value;
			},
			PickerFuDai(d) {
				this.FuDaiType = d.detail.value;
			},
			choice_file(d) {
				console.log(d);
				var index = d.currentTarget.dataset.index;
				var info = this.flie_net_list[index];
				var key = d.currentTarget.dataset.key; //1打开2选择
				console.log(info);
				if (key == 1) {
					//打开文件夹
					this.flie_net_list = [];
					this.file_page = 1;
					this.file_page_key = 1;
					this.file_page_in = 1;
					this.dir_count = 0;
					this.is_dir = 1;
					this.get_dir_in_list(info.id);
				} else {
					this.file_id = info.id;
					this.file_mod = '';
					this.get_file_info();
				}
				// if (info['is_dir'] == 1) { //夹
				//   //打开文件夹中的问题
				//   this.flie_net_list = [];
				//   this.file_page = 1;
				//   this.file_page_key = 1;
				//   this.file_page_in = 1;
				//   this.dir_count = 0;
				//   this.is_dir = 1;
				//   this.get_dir_in_list(info.id);
				// } else {
				//   this.file_id = info.id;
				//   this.file_mod = '';
				//   this.get_file_info();
				// }
			},
			get_dir_in_list(id) {
				var b = app.globalData.api_root + 'Storage/get_inside_list';
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				params.pid = id;
				params.page = this.file_page_in;
				var allMsg = that.flie_net_list;
				http.POST(b, {
					params: params,
					success: (res) => {
						console.log(res);
						if (res.data.code == 0) {
							for (var i = 0; i < res.data.list.length; i++) {
								allMsg.push(res.data.list[i]);
							}
							that.flie_net_list = allMsg;
							if (res.data.list.length == 0 || allMsg.length < 10) {
								that.file_di_msg = true;
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.data.msg,
								showCancel: false,
								success: (res) => {}
							})
						}

					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: (res) => {}
						})
					},
				})
			},
			open_file_d() {
				this.file_mod = true;
				this.flie_net_list = [];
				this.file_page = 1;
				this.file_page_key = 1;
				this.file_page_in = 1;
				this.dir_count = 0;
				this.is_dir = 0;
				this.get_net_list();
			},
			back_file_list() {
				this.flie_net_list = [];
				this.file_page = 1;
				this.file_page_key = 1;
				this.file_page_in = 1;
				this.dir_count = 0;
				this.is_dir = 0;
				this.get_net_list();
			},
			get_net_list() {
				var b = app.globalData.api_root + 'Storage/my_netdisc_belong';
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				params.page = this.file_page;
				params.page_k = this.file_page_key;
				var allMsg = that.flie_net_list;
				http.POST(b, {
					params: params,
					success: (res) => {
						console.log(res);
						that.dir_count = res.data.page;
						if (res.data.code == 0) {
							for (var i = 0; i < res.data.info.length; i++) {
								allMsg.push(res.data.info[i]);
							}
							that.flie_net_list = allMsg;

							if (res.data.info.length == 0 || allMsg.length < 10) {
								that.file_di_msg = true;
							}
						} else {
							uni.showModal({
								title: '提示',
								content: res.data.msg,
								showCancel: false,
								success: (res) => {}
							})
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: (res) => {}
						})
					},
				})
			},
			scroll_file_list() {
				if (this.dir_count >= 15) {
					this.file_page = this.file_page + 1;
				} else {
					this.file_page = this.file_page + 1;
					this.file_page_key = this.file_page_key + 1;
				}
				this.get_net_list();
				// if (this.is_search) {
				//   this.get_my_search_list();
				// } else {
				//   this.get_my_file();
				// }

			},
			hideFileMod() {
				this.file_page = 1;
				this.file_page_key = 1;
				this.file_mod = false;
			},
			set_phone(d) {
				this.my_phone = d.detail.phone;
				this.open_user_phone_input = !this.open_user_phone_input;
				this.open_phone = d.detail.bh;
			},
			get_phone(d) {
				this.my_phone = d.detail.phone;
			},
			// 计算图片宽度
			_handleComputedImage(e) {
				var that = this;
				uni.getSystemInfo({
					success: (res) => {
						const windowWidth = res.windowWidth;
						const width = windowWidth - 30;
						const imageWitdh = (width - 30) / 3;
						that.imageWitdh = imageWitdh;
					},
					fail: (err) => {
						console.log(err)
					}
				});


			},

			// 选择图片
			handleChooseImage(e) {
				var length = this.img_arr.length;
				if (length >= 9) {
					uni.showToast({
						title: "亲，最多只能选择九张图哦~",
						icon: "none",
						duration: 2000
					})
					return false;
				}
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var b = app.globalData.api_root + 'User/img_upload';
				uni.chooseMedia({
					count: 9 - that.img_arr.length,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'], //可选择原图或压缩后的图片
					sourceType: ['album', 'camera'], //可选择性开放访问相册、相机
					success: res => {
						uni.showLoading({
							title: '上传中...',
							mask: true,
						});
						let tempFilePaths = res.tempFiles;
						console.log(res);
						for (var i = 0, h = tempFilePaths.length; i < h; i++) {
							uni.uploadFile({
								url: b,
								filePath: tempFilePaths[i].tempFilePath,
								name: 'sngpic',
								header: {
									"content-type": "multipart/form-data",
								},
								formData: {
									"content-type": "multipart/form-data",
									'token': e.token,
									'openid': e.openid,
									'much_id': app.globalData.siteInfo.uniacid
								},
								success: (res) => {
									console.log(res.data);
									if (res.data == '') {
										uni.hideLoading();
										uni.showModal({
											title: '提示',
											content: '内存溢出，请稍候重试',
										})
										return;
									}
									var data = JSON.parse(res.data);
									console.log(data);
									if (data.status == 'error') {
										uni.hideLoading();
										uni.showModal({
											title: '提示',
											content: data.msg,
										})
										return;
									} else {
										var get_content = that.get_content;
										get_content.push({
											type: 'img',
											value: data.url,
											hide: 0
										});
										var img = that.img_arr;
										img.push(data.url);
										that.img_arr = img;
										that.get_content = get_content;
										uni.hideLoading();
									}
								},
								fail: (res) => {
									uni.showModal({
										title: '提示',
										content: '上传错误！',
									})
								}
							});
						}
					},
					fail: err => console.log(err)
				})
			},

			// 预览图片
			handlePreview(e) {
				let index = e.target.dataset.index;
				let img_arr = this.img_arr;
				uni.previewImage({
					current: img_arr[index], //当前预览的图片
					urls: img_arr, //所有要预览的图片数组
				})
			},

			// 删除图片
			handleDelete(e) {
				let index = e.target.dataset.index;
				let img_arr = this.img_arr;
				var content = this.get_content;
				for (var i = 0; i < content.length; i++) {
					if (content[i]['type'] == 'img' && content[i]['value'] == img_arr[index]) {
						content.splice(i, 1);
					}
				}
				img_arr.splice(index, 1);
				this.img_arr = img_arr;
				this.get_content = content;
				this._handleComputedArea();
			},

			// 计算movable-area的高度
			_handleComputedArea(e) {
				let that = this;
				uni.createSelectorQuery().selectAll('.image-choose-container').boundingClientRect(function(rect) {
					that.areaHeight = rect[0].height;
				}).exec()
			},

			// 计算每张图片的坐标
			_handleComputedPoints(e) {
				let that = this;
				var query = uni.createSelectorQuery();
				var nodesRef = query.selectAll(".image-item");
				nodesRef.fields({
					dataset: true,
					rect: true
				}, (result) => {
					that.pointsArr = result;
				}).exec()
			},

			// 长按图片
			handleLongTap(e) {
				// 计算每张图片的坐标
				this._handleComputedPoints();
				this.currentImg = e.currentTarget.dataset.url;
				this.currentIndex = e.currentTarget.dataset.index;
				this.hidden = false;
				this.flag = true;
				this.x = e.currentTarget.offsetLeft;
				this.y = e.currentTarget.offsetTop;
			},

			// 移动的过程中
			handleTouchMove(e) {
				let x = e.touches[0].pageX;
				let y = e.touches[0].pageY;
				// 首先先获得当前image-choose-container距离顶部的距离
				let that = this;
				uni.createSelectorQuery().selectAll('.image-choose-container').boundingClientRect(function(rect) {
					let top = rect[0].top;
					y = y - that.scrollTop - top;
					that.x = x;
					that.y = y;

				}).exec()
			},

			// 移动结束的时候
			handleTouchEnd(e) {
				if (!this.flag) {
					// 非长按情况下
					return;
				}
				let x = e.changedTouches[0].pageX;
				let y = e.changedTouches[0].pageY - this.scrollTop;
				const pointsArr = this.pointsArr;
				let data = this.img_arr;
				for (var j = 0; j < pointsArr.length; j++) {
					const item = pointsArr[j];
					if (x > item.left && x < item.right && y > item.top && y < item.bottom) {
						const endIndex = item.dataset.index;
						const beginIndex = this.currentIndex;
						//临时保存移动的目标数据
						let temp = data[beginIndex];
						//将移动目标的下标值替换为被移动目标的下标值
						data[beginIndex] = data[endIndex];
						//将被移动目标的下标值替换为beginIndex
						data[endIndex] = temp;
					}
				}
				this.img_arr = data;
				this.hidden = true;
				this.flag = false;
				this.currentImg = '';
			},

			open_all() {
				uni.navigateTo({
					url: '/yl_welore/pages/square/index?type=add',
				})
			},
			get_jiu(event) {
				this.Jiugongge = !this.Jiugongge;
			},
			cancel_unlock() {
				this.top_info = '';
				app.removeCache('name_card');
			},
			to_unlock() {
				uni.navigateTo({
					url: '/yl_welore/pages/packageD/nameplate/index?type=add',
				})
			},
			//输入投票名称
			input_advantage(item) {
				var txt = item.detail.value;
				var index = item.target.dataset.index;
				// 使用 $set 方法确保响应式更新
				this.$set(this.option[index], 'ballot_name', txt);
			},
			//多一个选项
			click_advantage() {
				var opc = this.option;
				opc.push({
					ballot_name: ''
				});
				this.option = opc;
			},
			textareaAInput(e) {
				var get_content = this.get_content;
				get_content[0]['value'] = e.detail.value;
				this.get_content = get_content;
			},
			//删除选项
			click_advantage_del() {
				var opc = this.option;
				if (opc.length <= 2) {
					uni.showToast({
						title: '至少保留两个选项',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				opc.splice(opc.length - 1, 1);
				this.option = opc;
			},
			//投票是否多选
			onChange_choice(event) {
				const detail = event.detail;
				this.option_choice = detail.value;
			},

			get_file_info() {
				var b = app.globalData.api_root + 'Storage/get_add_file_info';
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				params.file_id = this.file_id;
				http.POST(b, {
					params: params,
					success: (res) => {
						console.log(res);
						if (res.data.status == "success") {
							that.file_forw_info = res.data.info;
						} else {
							uni.showModal({
								title: '提示',
								content: res.data.msg,
								showCancel: false,
								success: (res) => {}
							})
							that.file_id = 0;
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: (res) => {}
						})
					},
				})
			},
			get_one_name_card(name_card) {
				var b = app.globalData.api_root + 'Nameplate/get_one_name_card';
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				params.name_id = name_card;
				http.POST(b, {
					params: params,
					success: (res) => {
						console.log(res);
						if (res.data.status == "success") {
							that.top_info = res.data.info
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: (res) => {}
						})
					},
				})
			},
			hide_hlep() { //隐藏域
				this.hide_hlep_mod = true;
			},
			get_switch(d) {
				var key = d.currentTarget.dataset.key;
				var check = d.detail.value == true ? 1 : 0;
				var get_content = this.get_content;
				get_content[key]['hide'] = check;
				this.get_content = get_content;
			},
			/**
			 * 获取活动人数
			 */
			active_set_num(d) {
				console.log(d);
				this.active_num = d.detail.value;
			},
			/**
			 * 获取活动地址
			 */
			get_position() {
				var that = this;
				uni.chooseLocation({
					success: (res) => {
						that.active_address = res.address;
						that.active_latitude = res.latitude;
						that.active_longitude = res.longitude;
					},
					fail: (r) => {
						console.log(r);
						if (r.errMsg == "chooseLocation:fail cancel") {
							that.is_position = false;
							return;
						}
						that.visible_pos = true;
						that.is_position = false;
					}
				})
			},
			/**
			 * 日历控件绑定函数 
			 * 点击日期返回
			 */
			onPickerChange(e) {
				var key = e.currentTarget.dataset.key;
				if (key == 1) {
					this.active_star_time = e.detail.dateString;
				}
				if (key == 2) {
					this.active_end_time = e.detail.dateString;
					this.option_end_time = e.detail.dateString;
				}
			},
			format(e) {
				let {
					name,
					value
				} = e.target.dataset
				if (!name) return
				// console.log('format', name, value)
				this.editorCtx.format(name, value)

			},
			onEditorReady() {
				const that = this
				uni.createSelectorQuery().select('#editor').context(function(res) {
					that.editorCtx = res.context
				}).exec()
			},
			onStatusChange(e) {
				const formats = e.detail
				this.formats = formats;
			},
			/**
			 * 点击的类型
			 */
			get_this_button(key) {
				console.log(key);
				var that = this
				var index = key.currentTarget.dataset.k;
				console.log(index);
				console.log(this.button_key);
				if (this.button_key == index && index == 2) {
					that.red_paper = false;
					//is_submit: false,
					that.button_key = 0;
					return;
				}
				if (this.button_key == index && index == 3) {
					that.money_paper = false;
					//is_submit: false,
					that.button_key = 0;
					return;
				}
				if (this.button_key == index && index == 4) {
					that.button_key = 0;
					return;
				}
				this.button_key = index;
				if (index == 1) {
					uni.chooseLocation({
						success: (res) => {
							console.log(res);
							that.position = res.address;
							that.position_name = res.name;
							that.address_latitude = res.latitude;
							that.address_longitude = res.longitude;
							that.ppooss = res.name;
							that.is_position = true;
						},
						fail: (r) => {
							console.log(r);
							if (r.errMsg == "chooseLocation:fail cancel") {
								that.is_position = false;
								that.button_key = 0;
								return;
							}
							that.visible_pos = true;
							that.is_position = false;
							that.button_key = 0;
						}
					})
				}
				if (index == 2) {
					this.red_paper = true;
					//is_submit: true,
					this.money_paper = false;
				}
				if (index == 3) {
					this.money_paper = true;
					this.red_paper = false;
					//is_submit: true,
				}
				if (index == 4) {
					this.money_paper = false;
					this.red_paper = false;
					//is_submit: true
				}
				if (index == 5) {
					uni.navigateTo({
						url: '/yl_welore/pages/packageA/conversation/index'
					})
				}
			},
			/**
			 * 关闭某些东西
			 */
			close_button(key) {
				var that = this
				var index = key.currentTarget.dataset.key;
				if (index == 1) {
					that.position = '';
					that.position_name = '';
					that.address_latitude = '';
					that.address_longitude = '';
					that.ppooss = '';
					that.is_position = false;
					that.button_key = 0;
				}
				if (index == 2) {
					that.red_paper = false;
					that.is_submit = false;
					that.button_key = 0;
					that.fuli_is = false;
					that.zong_red_money = 0;
					that.zong_red_count = 0;
					that.xian_red_money = 0;
				}
				if (index == 3) {
					that.money_paper = false;
					that.is_submit = false;
					that.button_key = 0;
					that.dingyue_is = false;
					that.xian_money = 0;
					that.money = 0;
					that.file_off_money = false;
				}
				if (index == 4) {
					that.is_open = false;
				}
				if (index == 5) {
					that.gambit_name = '';
					that.gambit_id = '';
					app.globalData.removeCache('gambit_card');
				}
			},
			/**
			 * 保存某些东西
			 */
			add_button(key) {
				var index = key.currentTarget.dataset.key;
				if (this.is_submit == true) {
					uni.showToast({
						title: '设置不成功哦',
						icon: 'none',
						duration: 2000
					})
					return;
				}
				//福利帖子
				if (index == 2) {
					if (this.dingyue_is == true) {
						uni.showToast({
							title: '您已设置订阅帖，不可再设置福利帖了',
							icon: 'none',
							duration: 2000
						})
						this.red_paper = false;
						this.button_key = 0;
						this.xian_money = 0;
						this.money = 0;
						return;
					}
					if (this.FuDaiType == 0) {
						if (parseFloat(this.zong_red_money) > parseFloat(this.user_info['conch'])) {
							uni.showToast({
								title: '所需余额不足',
								icon: 'none',
								duration: 2000
							})
							return;
						}
					} else {
						if (parseFloat(this.zong_red_money) > parseFloat(this.user_info['fraction'])) {
							uni.showToast({
								title: '所需余额不足',
								icon: 'none',
								duration: 2000
							})
							return;
						}
					}

					if (this.zong_red_money == 0) {
						this.red_paper = false;
						this.button_key = 0;
						this.fuli_is = false;
						this.xian_money = 0;
						this.money = 0;
						this.zong_red_count = 0;
						this.zong_red_money = 0;
						return;
					}
					this.red_paper = false;
					this.button_key = 0;
					this.fuli_is = true;
					this.xian_money = 0;
					this.money = 0;
				}
				if (index == 3) {
					if (this.fuli_is == true) {
						uni.showToast({
							title: '您已设置福利帖，不可再设置订阅帖了',
							icon: 'none',
							duration: 2000
						})
						this.money_paper = false;
						this.button_key = 0;
						this.zong_red_money = 0;
						this.zong_red_count = 0;
						this.xian_red_money = 0;
						return;
					}
					if (this.money == 0) {
						this.money_paper = false;
						this.button_key = 0;
						this.dingyue_is = false;
						this.zong_red_money = 0;
						this.zong_red_count = 0;
						this.xian_red_money = 0;
						this.file_off_money = false;
						return;
					}
					this.money_paper = false;
					this.button_key = 0;
					this.dingyue_is = true;
					this.zong_red_money = 0;
					this.zong_red_count = 0;
					this.xian_red_money = 0;
				}
			},
			/**
			 * 获取类型
			 */
			add_type() {
				var get_content = this.get_content;
				get_content.push({
					type: 'text',
					value: '',
					hide: 0
				});
				this.get_content = get_content;
			},
			/**
			 * 删除指定
			 */
			del_add_type(d) {
				var get_content = this.get_content;
				var index = d.currentTarget.dataset.key;
				var img = this.img_arr;
				for (var i = 0; i < img.length; i++) {
					if (get_content[index]['type'] == 'img' && img[i] == get_content[index]['value']) {
						img.splice(i, 1);
					}
				}
				get_content.splice(index, 1);
				this.img_arr = img;
				this.get_content = get_content;
			},
			onChangePosition(event) {
				var that = this;
				const detail = event.detail;
				this.is_position = detail.value;
				if (detail.value == true) {
					uni.chooseLocation({
						success: (res) => {
							console.log(res);
							that.position = res.address;
							that.position_name = res.name;
							that.address_latitude = res.latitude;
							that.address_longitude = res.longitude;
							that.ppooss = res.name;
						},
						fail: (r) => {
							console.log(r);
							if (r.errMsg == "chooseLocation:fail cancel") {
								that.is_position = false;
								return;
							}
							that.visible_pos = true;
							that.is_position = false;
						}
					})
				}

			},
			get_diy() {
				var b = app.globalData.api_root + 'User/get_diy';
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				params.uid = e.uid;
				http.POST(b, {
					params: params,
					success: (res) => {
						console.log(res);
						that.version = res.data.version;
						that.open_file = res.data.open_file;
						that.phone_config = res.data.phone_config;
						that.expression = res.data.expression;
						that.Jiugongge = res.data.user_vip.rel_paper_img_style == 0 ? false : true;
						if (res.data.phone_config == 1) {
							that.open_user_phone_input = true;
							that.open_phone = true;
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: (res) => {}
						})
					},
				})
			},
			/**
			 * 选择福利类型
			 */
			set_red_type() {
				this.red_type = this.red_type == 1 ? 0 : 1;
				this.zong_red_money = '0.00';
				this.xian_red_money = '';
			},
			/**
			 * 计算普通福利
			 */
			get_red_money_d(d) {
				console.log(d);
				var money = d.detail.value;
				if (money == '') {
					return;
				}
				var reg = /^(\.*)(\d+)(\.?)(\d{0,2}).*$/g;
				if (reg.test(money)) { //正则匹配通过，提取有效文本
					money = money.replace(reg, '$2$3$4');
				} else { //正则匹配不通过，直接清空
					money = '';
				}
				var count = this.zong_red_count;
				var zong = (money * count).toFixed(2);
				this.xian_red_money = money;
				this.zong_red_money = zong;
			},
			/**
			 * 计算付费帖子金额
			 */
			get_paper_money(d) {
				console.log(d);
				var money = d.detail.value;
				if (money == '') {
					this.money = 0;
					this.xian_money = 0;
					this.is_submit = false;
					return;
				}
				var reg = /^(\.*)(\d+)(\.?)(\d{0,2}).*$/g;
				if (reg.test(money)) { //正则匹配通过，提取有效文本
					money = money.replace(reg, '$2$3$4');
				} else { //正则匹配不通过，直接清空
					money = '0.00';
				}

				this.money = money;
				this.xian_money = parseFloat(money).toFixed(2);
				this.is_submit = false;
			},
			/**
			 * 总金额
			 */
			get_red_money(d) {
				var money = d.detail.value;
				if (money == '') {
					this.zong_red_money = 0;
					this.xian_red_money = '';
					this.is_submit = false;
					return;
				}
				var reg = /^(\.*)(\d+)(\.?)(\d{0,2}).*$/g;
				if (reg.test(money)) { //正则匹配通过，提取有效文本
					money = money.replace(reg, '$2$3$4');
					this.zong_red_money = parseFloat(money).toFixed(2);
					this.xian_red_money = money;
				} else { //正则匹配不通过，直接清空
					money = '0';
					this.zong_red_money = '0.00';
					this.xian_red_money = '';
				}
				var count = this.zong_red_count;
				if (count != '') {
					if ((money / count) < 0.01) {
						uni.showToast({
							title: '单个福利不可低于0.01',
							icon: 'none',
							duration: 2000
						})
						this.is_submit = true;
						return;
					} else {
						this.is_submit = false;
					}
				}
			},
			get_red_count(d) {
				var count = d.detail.value;
				if (count == '') {
					return;
				}
				if (count == 0) {
					this.zong_red_count = 1;
					return;
				}
				var reg = /^[0-9]*$/g;
				if (!reg.test(count)) { //正则匹配通过，提取有效文本
					count = '1';
				}
				this.zong_red_count = count;

				if (this.red_type == 1) {
					if (this.xian_red_money != '') {
						var xian_red_money = this.xian_red_money;
						if ((xian_red_money / count) < 0.01) {
							uni.showToast({
								title: '单个福利不可低于0.01',
								icon: 'none',
								duration: 2000
							})
							this.is_submit = true;
							return;
						} else {
							this.is_submit = false;
						}
					}
				} else {
					var zong = (this.xian_red_money * count).toFixed(2);
					this.zong_red_money = zong;
					this.is_submit = false;
				}


			},
			/**
			 * 选择发布的圈子
			 */
			select_qq_id(d) {
				var index = d.currentTarget.dataset.index;
				var info = this.navRightItems[index];
				var user_info = this.user_info;
				if (user_info['admin'] == 0 && info['da_or_xiao'] == 'no') {
					if (user_info['level'] < info['release_level']) {
						uni.showToast({
							title: '用户等级不足',
							icon: 'none',
							duration: 2000
						})
						return;
					}
				}

				this.title = '发布到' + d.currentTarget.dataset.name;
				this.showLeft = !this.showLeft;
				this.fa_class = d.currentTarget.dataset.id;
				this.get_hidden = !this.get_hidden;
			},
			toggleLeft() {
				this.showLeft = !this.showLeft;
				this.get_hidden = this.get_hidden == true ? false : true;
			},
			lower() {
				this.page = this.page + 1;
				this.get_right_item();

			},
			/**
			 * 获取所有圈子
			 */
			get_left_needle() {
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				var b = app.globalData.api_root + 'User/get_left_needle';

				http.POST(b, {
					params: params,
					success: (res) => {
						if (res.data.status == "success") {
							that.navLeftItems = res.data.info
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none',
								duration: 2000
							})
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: (res) => {}
						})
					},
				})
			},
			/**
			 * 右边的数据
			 */
			get_right_item() {
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				params.uid = e.uid;
				params.get_id = that.curNav;
				params.cat_add = 1;
				params.page = that.page;
				var b = app.globalData.api_root + 'User/get_right_needle';
				var allMsg = that.navRightItems;
				http.POST(b, {
					params: params,
					success: (res) => {
						console.log(res);
						if (res.data.status == "success") {
							if (res.data.set_null == 0) {
								for (var i = 0; i < res.data.info.length; i++) {
									allMsg.push(res.data.info[i]);
								}
								that.navRightItems = allMsg
							} else {
								that.navRightItems = res.data.info
							}

						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none',
								duration: 2000
							})
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: (res) => {}
						})
					},
				})
			},
			//获取用户信息
			get_user_info() {
				var b = app.globalData.api_root + 'User/get_user_info';
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				params.token = e.token;
				params.openid = e.openid;
				http.POST(b, {
					params: params,
					success: (res) => {
						console.log(res);
						if (res.data.status == 'success') {
							that.user_info = res.data.info;
							that.is_vip = res.data.info.is_vip
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none',
								duration: 2000
							})
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: (res) => {}
						})
					},
				})
			},
			set_tuya() {
				this.cleardraw();
				//判断是否打开VIP
				if (this.copyright['graffiti_member'] == 1) {
					if (this.user_info['is_vip'] == 1) {
						this.tuya = !this.tuya
					} else {
						uni.showToast({
							title: 'VIP专属',
							icon: 'none',
							duration: 2000
						})
						return;
					}
				} else {
					this.tuya = !this.tuya
				}
			},
			/**
			 * 发布
			 */
			submit() {
				//true 用贝壳
				var that = this;
				if (this.fa_class == 0) {
					uni.showToast({
						title: '请选择发布的' + this.design['landgrave'],
						icon: 'none',
						duration: 2000
					})
					this.showLeft = true;
					this.get_hidden = false;
					return;
				}
				if (this.FuDaiType == 1) { //0贝壳
					if (parseFloat(this.zong_red_money) > parseFloat(this.user_info['fraction'])) {
						uni.showToast({
							title: '余额不足',
							icon: 'none',
							duration: 2000
						})
						return;
					}
				} else {
					if (parseFloat(this.zong_red_money) > parseFloat(this.user_info['conch'])) {
						uni.showToast({
							title: '余额不足',
							icon: 'none',
							duration: 2000
						})
						return;
					}
				}

				if (this.startX == 0 && this.tuya) {
					uni.showToast({
						title: '画布内容不能为空！',
						icon: 'none',
						duration: 2000
					})
					return false;
				}
				if (this.fa_type == 6) {
					for (var i = 0; i < this.option.length; i++) {
						if (this.option[i].ballot_name == '') {
							uni.showToast({
								title: '选项' + (i + 1) + '不能为空！',
								icon: 'none',
								duration: 2000
							})
							return;
						}
					}
				}

				uni.showLoading({
					title: '发布中...',
				})

				that.is_submit = true;
				var e = app.globalData.getCache("userinfo");
				var params = new Object();
				if (this.title_value == '' && this.title_arbor == 1) {
					uni.showModal({
						title: '提示',
						content: '标题不能为空'
					})
					that.is_submit = false;
					uni.hideLoading();
					return;
				}
				if (this.fa_type == 0) {

					if (this.get_content.length == 0 || this.get_content[0]['value'] == '') {
						uni.showModal({
							title: '提示',
							content: '内容不能为空'
						})
						uni.hideLoading();
						that.is_submit = false;
						return;
					}
				}
				if (this.open_phone && this.my_phone == '') {
					uni.hideLoading();
					uni.showModal({
						title: '提示',
						content: '联系方式不能为空'
					})
					that.is_submit = false;
					return;
				}
				//电话
				params.my_phone = this.my_phone;
				params.open_phone = this.open_phone ? 1 : 0;
				//内容
				params.title = this.title_value;
				params.color = this.title_color;
				params.content = this.text;
				params.img_arr = JSON.stringify(this.img_arr);
				params.top_img_arr = this.top_img_arr;
				params.uid = e.uid;
				params.token = e.token;
				params.openid = e.openid;
				//转发
				params.is_open = this.is_open == false ? 1 : 0;
				//九宫格
				params.img_show_type = this.Jiugongge == true ? 1 : 0;
				//视频高度
				params.vd_width = this.vd_width;
				params.vd_height = this.vd_height;
				//地址
				params.option = JSON.stringify(this.option);
				params.option_end_time = this.option_end_time;
				//身份名牌
				if (this.top_info == '') {
					params.name_card = 0;
				} else {
					params.name_card = this.top_info['id'];
				}
				if (this.fa_type == 3) {
					params.type = 0;
				} else if (this.fa_type == 4) {
					params.type = 3;
				} else if (this.fa_type == 6) {
					if (this.option_choice) {
						params.type = 5;
					} else {
						params.type = 4;
					}
				} else {
					params.type = this.fa_type;
				}

				params.fa_class = this.fa_class;
				params.file_ss = this.file_ss;
				//新图文发布
				params.get_content = this.get_content;
				//地理位置
				params.position_name = this.position_name;
				params.position = this.position;
				params.address_latitude = this.address_latitude;
				params.address_longitude = this.address_longitude;
				//话题ID
				params.gambit_id = this.gambit_id;
				//福袋
				if (this.fuli_is) {
					params.red_paper = this.fuli_is == true ? 1 : 0;
					params.red_type = this.red_type;
					params.zong_red_count = this.zong_red_count;
					params.zong_red_money = this.zong_red_money;
					params.fu_dai_type = this.FuDaiType; //0贝壳
				}
				//订阅
				if (this.dingyue_is) {
					params.money_paper = this.dingyue_is == true ? 1 : 0;
					params.zong_paper_money = this.money;
					params.ding_yue_type = this.DingYueType; //0贝壳
				}
				//网盘
				params.file_id = this.file_id;
				params.file_off_fowd = this.file_off_fowd ? 1 : 0;
				params.file_off_money = this.file_off_money ? 1 : 0;
				//解析
				params.jiexi_text = this.jiexi_text;
				params.is_jiexi = this.is_jiexi ? 1 : 0;
				//视频号
				params.feedToken = this.feedToken;
				if (this.shipinhao_show == true) {
					params.type = 6;
				} else {
					if (this.fa_type == 2) {
						if (this.file == '') {
							uni.showModal({
								title: '提示',
								content: '请添加视频',
							})
							uni.hideLoading();
							that.is_submit = false;
							return;
						}
					}
				}

				if (this.fa_type == 4) {
					if (this.active_address == '') {
						uni.showToast({
							title: '请添加活动地址',
							icon: 'none',
							duration: 2000
						})
						uni.hideLoading();
						that.is_submit = false;
						return;
					}
					if (this.active_star_time == '') {
						uni.showToast({
							title: '请添加开始时间',
							icon: 'none',
							duration: 2000
						})
						uni.hideLoading();
						that.is_submit = false;
						return;
					}
					if (this.active_end_time == '') {
						uni.showToast({
							title: '请添加结束时间',
							icon: 'none',
							duration: 2000
						})
						uni.hideLoading();
						that.is_submit = false;
						return;
					}
					if (this.active_num == '') {
						uni.showToast({
							title: '请添加活动人数',
							icon: 'none',
							duration: 2000
						})
						uni.hideLoading();
						that.is_submit = false;
						return;
					}
					params.active_address = this.active_address;
					params.active_star_time = this.active_star_time;
					params.active_end_time = this.active_end_time;
					params.active_latitude = this.active_latitude;
					params.active_longitude = this.active_longitude;
					params.active_num = this.active_num;
				}
				console.log(params);
				if (this.fa_type == 1 || this.fa_type == 2) {
					params.user_file = this.file;
					//that.add_submit(params);
				}
				if (this.tuya) {
					//生成图片
					uni.canvasToTempFilePath({
						canvasId: 'canvas',
						success: (res) => {
							uni.uploadFile({
								url: app.globalData.api_root + 'User/img_upload',
								filePath: res.tempFilePath,
								name: 'sngpic',
								header: {
									"content-type": "multipart/form-data",
								},
								formData: {
									"content-type": "multipart/form-data",
									'token': e.token,
									'openid': e.openid,
									'much_id': app.globalData.siteInfo.uniacid
								},
								success: (res) => {
									console.log(res);
									var data = JSON.parse(res.data);
									var get_content = that.get_content;
									get_content.push({
										type: 'img',
										value: data.url,
										hide: 0
									});
									params.get_content = get_content;
									// params.get_content.push({
									//   type: 'img',
									//   value: data.url,
									//   hide: 0
									// });
									that.add_submit(params);
								},
								fail: (res) => {
									uni.showToast({
										title: res.data.msg,
										icon: '上传错误！',
										duration: 2000
									})
								}
							});
						}
					})
				} else {
					that.add_submit(params);
				}
				// console.log(params);

				uni.hideLoading();
			},
			/**
			 * 统一发布
			 */
			add_submit(params) {
				console.log(params);
				this.is_submit = true;
				params.get_content = JSON.stringify(params.get_content);
				var that = this;
				var b = app.globalData.api_root + 'User/add_circle_new';
				http.POST(b, {
					params: params,
					success: (res) => {
						console.log(res);
						if (res.data.status == "success") {
							uni.showModal({
								title: '提示',
								content: res.data.msg,
								success(res) {
									if (res.confirm) {
										that.get_subscribe();
									} else if (res.cancel) {
										that.get_subscribe();
									}
								}
							})
						} else if (res.data.status == "error") {
							that.is_submit = false;
							uni.showModal({
								title: '提示',
								content: res.data.msg,
							})
							uni.hideLoading();
						} else if (res.data.status == "name_error") {
							that.is_submit = false;
							uni.showModal({
								title: '提示',
								content: res.data.msg,
							})
							app.globalData.removeCache('name_card');
							uni.hideLoading();
						} else {
							that.is_submit = false;
							that.top_info = '';
							uni.showModal({
								title: '提示',
								content: res.data.msg,
							})
							uni.hideLoading();

						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: (res) => {}
						})
					},
				})
			},
			/**
			 * 是否开启转发
			 */
			onChange(event) {
				console.log(event);
				const detail = event.detail;
				this.is_open = detail.value;
				this.button_key = 0;

			},
			get_subscribe() {
				var that = this;
				var subscribe = app.globalData.getCache("subscribe");
				if (subscribe && subscribe['YL0001'] && subscribe['YL0002'] && subscribe['YL0003']) {
					app.globalData.authorization(subscribe['YL0001'], subscribe['YL0002'], subscribe['YL0003'], (res) => {
						setTimeout(() => {
							that.is_submit = false;
							uni.navigateBack();
						}, 1000);
					})
				} else {
					setTimeout(() => {
						that.is_submit = false;
						uni.navigateBack();
					}, 1000);
				}
			},
			/**
			 * 是否开启福利帖子
			 */
			onChange_red_paper(event) {
				console.log(event);
				const detail = event.detail;
				this.red_paper = detail.value;
				this.is_submit = detail.value;
				this.money_paper = false;

			},
			/**
			 * 是否开启付费帖子
			 */
			onChange_money_paper(event) {
				const detail = event.detail;
				this.money_paper = detail.value;
				this.red_paper = false;
				this.is_submit = detail.value;

			},
			/**
			 * 隐藏选择颜色
			 */
			hideModal() {
				this.text_color = false;
				this.get_hidden = true;
				this.ExModal = false;
			},
			/**
			 * 标题颜色
			 */
			color_select() {
				this.text_color = true;
				this.get_hidden = false;
			},
			/**
			 * 选择颜色
			 */
			handleClick4(e) {
				console.log(e);
				var index = e.currentTarget.dataset.index;
				this.text_color = false;
				this.get_hidden = true;
				this.title_color = this.actions4[index]['color']
			},
			/**
			 * 标题
			 */
			set_title_value(e) {
				var title = e.detail.value;
				this.title_value = title;
			},
			/**
			 * 是否显示标题
			 */
			set_title() {
				var is_title = this.is_title;
				if (is_title) {
					this.is_title = false;
				} else {
					this.is_title = true;
				}
			},
			/**
			 * 录制视频
			 */
			add_video() {
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var sizeType = [];
				//0关闭1开启
				if (that.copyright['video_compression_setting'] == 0) {
					sizeType = ['original'];
				} else {
					sizeType = ['compressed'];
				}
				console.log(sizeType);
				uni.chooseMedia({
					count: 1,
					sizeType: sizeType,
					mediaType: ['video'],
					sourceType: ['album', 'camera'],
					maxDuration: 60,
					//compressed: true,
					success(ress) {
						console.log(ress);
						var info = ress.tempFiles[0];
						that.vd_height = info.height;
						that.vd_width = info.width;
						if (that.copyright['video_setting'] < info.duration) {
							uni.showToast({
								title: '视频限制为' + that.copyright['video_setting'] + '秒！请重新上传',
								icon: 'none',
								duration: 2000
							})
							return;
						}
						that.loadModal = true;
						// 重置进度条和状态
						that.VideoTask = 0;
						that.ImgTask = 0;
						that.VideoUploadSuccess = false;
						that.ImgUploadSuccess = false;
						// 上传完成计数器
						var uploadCompleteCount = 0;
						// var params = new Object();
						var params = [];
						for (let i = 0; i <= 1; i++) {
							if (i == 0) {
								var f = info.tempFilePath; // 视频文件
							} else {
								var f = info.thumbTempFilePath; // 封面图文件
							}
							params[i] = uni.uploadFile({
								url: app.globalData.api_root + 'User/img_upload',
								filePath: f,
								name: 'sngpic',
								header: {
									"content-type": "multipart/form-data",
								},
								formData: {
									"content-type": "multipart/form-data",
									'token': e.token,
									'openid': e.openid,
									'much_id': app.globalData.siteInfo.uniacid
								},
								success: (res) => {
									var data = JSON.parse(res.data);
									console.log(data);
									if (data.status == 'error') {
										uni.showModal({
											title: '提示',
											content: data.msg,
											showCancel: false,
											success: (res) => {}
										})
										that.loadModal = false;
										return;
									}

									// 增加上传完成计数
									uploadCompleteCount++;

									if (i == 0) {
										// 视频文件上传完成
										that.file = data.url;
										that.is_jiexi = false;
										that.VideoUploadSuccess = true;
									} else {
										// 封面图文件上传完成
										that.top_img_arr = that.top_img_arr.concat(data.url);
										that.ImgUploadSuccess = true;
									}

									// 只有当两个文件都上传完成时才关闭模态框
									if (uploadCompleteCount >= 2) {
										that.loadModal = false;
									}
								},
								fail: (res) => {
									console.log(res);
									uni.showToast({
										title: '上传错误！',
										icon: 'none',
										duration: 2000
									})
									that.loadModal = false;
								}
							});
							params[i].onProgressUpdate((res) => {
								// 修正进度变量赋值：i == 0 是视频，i == 1 是封面图
								if (i == 0) {
									that.VideoTask = res.progress; // 视频进度
								}
								if (i == 1) {
									that.ImgTask = res.progress; // 封面图进度
								}
							})
							params[i].onHeadersReceived((res) => {
								console.log(res);
							})
						}


					},
					complete: (res) => {
						uni.hideLoading();
					},
				})
			},
			//开始录音的时候
			start() {
				var that = this;
				that.file_ss = 0;
				that.star_recorder = true;
				that.show_audio = true;
				//开始录音
				recorderManager.start(options);
				recorderManager.onStart(() => {
					recordTimeInterval = setInterval(function() {
						var ss = that.file_ss + 1;
						if (ss >= 300) {
							that.touchEnd();
						}
						that.file_ss = ss;
						that.flie_ss_name = that.formatSeconds(ss)
					}, 1000)
				});
				//错误回调
				recorderManager.onError((res) => {
					uni.hideLoading();
					uni.hideToast();
					console.log(res);
				})
			},
			/**
			 * 计算文字长度
			 */
			get_text_len(e) {
				console.log(e);
				var length = e.detail.value;
				var index = e.target.dataset.key;
				var get_content = this.get_content;
				get_content[index]['value'] = length;
				this.text = length;
				this.get_content = get_content;
			},
			//播放声音
			play() {
				var that = this;
				innerAudioContext.autoplay = false
				innerAudioContext.src = this.tempFilePath,
					innerAudioContext.play();
				console.log(innerAudioContext);
				this.is_voice = true;
				innerAudioContext.onEnded((res) => {
					this.is_voice = false;
					this.is_time = 0;
					this.is_time_name = '00:00';
				});
				innerAudioContext.onTimeUpdate((r) => {
					if (this.is_time < this.file_ss) {
						var k = this.is_time + 1;
						that.is_time = k;
						that.is_time_name = this.formatSeconds(k);
					}
				});
				innerAudioContext.onError((res) => {
					console.log(res.errMsg)
					console.log(res.errCode)
				});
			},
			formatSeconds(value) {
				let result = parseInt(value)
				let m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result /
					60 % 60))
				let s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60))
				result = `${m}:${s}`
				return result
			},
			/**
			 * 停止
			 */
			stop_yin(e) {
				innerAudioContext.stop();
				this.is_voice = false;
			},
			handleClose() {
				this.visible2 = false;
				this.visible_pos = false;
				this.hide_hlep_mod = false;
			},
			handleOk() {
				uni.openSetting();
				this.visible2 = false;
				this.visible_pos = false;
			},
			touchStart() {
				var that = this;
				uni.getSetting({
					success(res) {
						if (res.authSetting['scope.record'] == false) {
							uni.authorize({
								scope: 'scope.record',
								fail() {
									that.visible2 = true;
									that.scope_record = false;
								}
							})
						} else {
							that.scope_record = true;
						}
					}
				})
				if (that.scope_record == false) {
					return false;
				}
				uni.showToast({
					title: '正在录音',
					duration: 9999999,
					image: '/yl_welore/style/icon/yuyin.png',
					mask: false
				});
				this.start();
			},


			touchEnd() {
				uni.hideToast();
				this.stop()
			},

			//停止录音
			stop() {
				uni.showLoading({
					title: '上传中...',
					mask: true,
				})
				var that = this;
				var e = app.globalData.getCache("userinfo");
				recorderManager.stop();
				recorderManager.onStop((res) => {
					if (this.file_ss < 3) {
						uni.hideLoading();
						uni.showToast({
							duration: 1500,
							title: '录音时间太短了',
							mask: false
						});
						clearInterval(recordTimeInterval);
						recordTimeInterval = "";
						return;
					}
					this.tempFilePath = res.tempFilePath;
					console.log('停止录音', res)
					that.star_recorder = false;
					that.tempFilePath = res.tempFilePath;
					uni.uploadFile({
						url: app.globalData.api_root + 'User/img_upload',
						filePath: res.tempFilePath,
						name: 'sngpic',
						header: {
							"content-type": "multipart/form-data",
						},
						formData: {
							"content-type": "multipart/form-data",
							'token': e.token,
							'openid': e.openid,
							'much_id': app.globalData.siteInfo.uniacid
						},
						success: (res) => {
							console.log(res);
							var data = JSON.parse(res.data);
							console.log(data);
							if (data.code == 1) {
								uni.hideLoading();
								uni.showToast({
									title: data.msg,
									icon: 'none',
									duration: 2000
								})
							}
							that.file = data.url
							uni.hideLoading();
						},
						fail: (res) => {
							console.log(res);
							uni.showToast({
								title: '上传错误！',
								icon: 'none',
								duration: 2000
							})
						}
					});
					clearInterval(recordTimeInterval);
					recordTimeInterval = "";
				})
			},
			DelImg(e) {
				this.img_arr.splice(e.currentTarget.dataset.index, 1);
			},
			/**
			 * 上传图片
			 */
			chooseImage() {
				var length = this.img_arr.length;
				// if (length >= 9) {
				//   uni.showToast({
				//     title: "亲，最多只能选择九张图哦~",
				//     icon: "none",
				//     duration: 2000
				//   })
				//   return false;
				// }
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var b = app.globalData.api_root + 'User/img_upload';
				uni.chooseMedia({
					count: 9 - that.img_arr.length,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
					success: (res) => {
						console.log(res);
						uni.showLoading({
							title: '上传中...',
							mask: true,
						});
						let tempFilePaths = res.tempFiles;

						for (var i = 0, h = tempFilePaths.length; i < h; i++) {
							uni.uploadFile({
								url: b,
								filePath: tempFilePaths[i].tempFilePath,
								name: 'sngpic',
								header: {
									"content-type": "multipart/form-data",
								},
								formData: {
									"content-type": "multipart/form-data",
									'token': e.token,
									'openid': e.openid,
									'much_id': app.globalData.siteInfo.uniacid
								},
								success: (res) => {
									console.log(res);
									if (res.data == '') {
										uni.hideLoading();
										uni.showModal({
											title: '提示',
											content: '内存溢出，请稍候重试',
										})
										return;
									}
									var data = JSON.parse(res.data);
									console.log(data);
									if (data.status == 'error') {
										uni.hideLoading();
										uni.showModal({
											title: '提示',
											content: data.msg,
										})
										return;
									} else {
										var get_content = that.get_content;
										get_content.push({
											type: 'img',
											value: data.url,
											hide: 0
										});
										that.img_arr = that.img_arr.concat(data.url);
										that.get_content = get_content;
										uni.hideLoading();
									}
								},
								fail: (res) => {
									uni.showModal({
										title: '提示',
										content: '上传错误！',
									})
								}
							});
						}
					}
				})
			},
			/**
			 * 上传主图
			 */
			previewOneImage() {
				var that = this;
				var e = app.globalData.getCache("userinfo");
				var b = app.globalData.api_root + 'User/img_upload';
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
					success: (res) => {
						uni.showLoading({
							title: '上传中...',
							mask: true,
						});
						var tempFilePaths = res.tempFilePaths;
						uni.uploadFile({
							url: b,
							filePath: tempFilePaths[0].tempFilePath,
							name: 'sngpic',
							header: {
								"content-type": "multipart/form-data",
							},
							formData: {
								"content-type": "multipart/form-data",
								'token': e.token,
								'openid': e.openid,
								'much_id': app.globalData.siteInfo.uniacid
							},
							success: (res) => {
								console.log(res);
								var data = JSON.parse(res.data);
								console.log(data);
								if (data.status == 'error') {
									uni.showToast({
										title: data.msg,
										icon: 'none',
										duration: 2000
									})
								} else {
									that.top_img_arr = that.top_img_arr.concat(data.url);
									that.img_botton = false;
									uni.hideLoading();
								}
								//console.log(that.img_botton);
							},
							fail: (res) => {
								uni.showToast({
									title: '上传错误！',
									icon: 'none',
									duration: 2000
								})
							}
						});

					}
				})
			},
			/**
			 * 删除图片
			 */
			clearOneImage(e) {
				var that = this;
				var index = e.target.dataset['index'];
				var notes = that.top_img_arr;
				notes.splice(index, 1);
				that.top_img_arr = notes;
				that.img_botton = true;
			},
			/**
			 * 删除图片
			 */
			clearImage(e) {
				var that = this;
				var index = e.target.dataset['index'];
				var notes = that.img_arr;
				notes.splice(index, 1);
				that.img_arr = notes
				that.img_length = that.img_length + 1
				if (that.img_length > 0) {
					that.img_botton = true
				}
			},
			/**
			 * 预览图片
			 */
			previewImage(e) {
				console.log(e);

				var img_list = this.img_arr;
				var img = [];
				for (var i = 0; i < img_list.length; i++) {
					img.push(img_list[i]);
				}

				uni.previewImage({
					current: e.target.dataset.src, // 当前显示图片的http链接
					urls: img // 需要预览的图片http链接列表
				})
			},
			color_ssss(e) {
				this.cccc = e.currentTarget.dataset.color
			},
			canvasIdErrorCallback(e) {

			},
			canvasStart(e) {
				//得到触摸点的坐标

				this.startX = e.changedTouches[0].x

				this.startY = e.changedTouches[0].y

				this.context.setStrokeStyle(this.cccc)

				this.context.setLineWidth(7)

				this.context.setLineCap('round') // 让线条圆润 

				this.context.beginPath()

			},
			canvasMove(e) {
				var startX1 = e.changedTouches[0].x

				var startY1 = e.changedTouches[0].y

				this.context.moveTo(this.startX, this.startY)

				this.context.lineTo(startX1, startY1)

				this.context.stroke()

				this.startX = startX1;

				this.startY = startY1;


				//只是一个记录方法调用的容器，用于生成记录绘制行为的actions数组。context跟不存在对应关系，一个context生成画布的绘制动作数组可以应用于多个

				uni.drawCanvas({

					canvasId: 'canvas',

					reserve: true,

					actions: this.context.getActions() // 获取绘图动作数组

				})
			},
			canvasEnd(event) {
				isButtonDown = false;
			},
			cleardraw() {
				this.startX = 0;
				this.startY = 0;
				this.context = uni.createCanvasContext('canvas');
				this.context.clearRect(0, 0, canvasw, canvash);
				this.context.draw();
			},
			/**
			 * 用户点击右上角分享
			 */
			onShareAppMessage() {
				var forward = app.globalData.forward;
				console.log(forward);
				if (forward) {
					return {
						title: forward.title,
						path: '/yl_welore/pages/index/index',
						imageUrl: forward.reis_img,
					}
				} else {
					return {
						title: '您的好友给您发了一条信息',
						path: '/yl_welore/pages/index/index',
					}
				}
			},
			_navback() {
				var pages = getCurrentPages();
				var Page = pages[pages.length - 1]; //当前页
				var prevPage = pages[pages.length - 2]; //上一个页面
				if (pages.length == 1) {
					uni.reLaunch({
						url: '/yl_welore/pages/index/index',
					})
					return;
				}
				// prevPage.setData({
				//   show: false
				// })
				uni.navigateBack()
			},
		},



	}
</script>
<style>
	@import url("../../../colorui/icon1.css");

	/* 页面整体样式 */
	page {
		background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #f0f8ff 100%);
		min-height: 100vh;
	}

	.page-container {
		background: transparent;
	}

	/* 标题区域样式 */
	.title-content {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12rpx;
	}

	.title-emoji {
		font-size: 32rpx;
		line-height: 1;
	}

	.title-text {
		color: #000000;
		font-weight: 600;
		font-size: 36rpx;
	}

	/* 发布到区域样式 */
	.publish-to-card {
		background: #ffffff;
		margin: 20rpx;
		border-radius: 24rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.1);
	}

	.publish-to-content {
		display: flex;
		align-items: center;
		font-size: 28rpx;
	}

	.publish-to-emoji {
		font-size: 32rpx;
		margin-right: 16rpx;
	}

	.publish-to-text {
		flex: 1;
		color: #333333;
		font-weight: 500;
	}

	/* 通用区域样式 */
	.content-section {
		padding: 0 20rpx;
		margin-top: 20rpx;
	}

	.section-emoji {
		font-size: 32rpx;
		margin-right: 12rpx;
	}

	.section-title {
		color: #333333;
		font-weight: 600;
		font-size: 32rpx;
	}

	/* 标题输入区域 */
	.title-input-card {
		background: #ffffff;
		margin: 20rpx;
		border-radius: 24rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.1);
	}

	.title-input-header {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.modern-input {
		background: #f8f9fa;
		border: 2rpx solid #e9ecef;
		border-radius: 16rpx;
		padding: 24rpx 32rpx;
		font-size: 28rpx;
		color: #333333;
		width: 100%;
		transition: all 0.3s ease;
		height: 80rpx;
	}

	.modern-input:focus {
		border-color: #1cbbb4;
		background: #ffffff;
		box-shadow: 0 0 0 6rpx rgba(28, 187, 180, 0.1);
	}

	.color-select-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 80rpx;
		height: 80rpx;
		background: linear-gradient(135deg, #ffd700, #ffed4e);
		border-radius: 50%;
		box-shadow: 0 4rpx 16rpx rgba(255, 215, 0, 0.3);
		font-size: 45rpx;
		line-height: 80rpx;
	}

	/* 涂鸦区域样式 */
	.drawing-card {
		background: #ffffff;
		margin: 20rpx;
		border-radius: 24rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.1);
	}

	.drawing-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24rpx;
	}

	.clear-canvas-btn {
		display: flex;
		align-items: center;
		gap: 8rpx;
		background: linear-gradient(135deg, #ff6b6b, #ee5a52);
		color: white;
		padding: 12rpx 24rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
	}

	.modern-canvas {
		margin: 0 auto;
		width: 90%;
		height: 15em;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
		border: 2rpx solid #e9ecef;
	}

	.color-palette {
		margin-top: 30rpx;
		padding: 0 20rpx;
	}

	.palette-title {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}

	.palette-emoji {
		font-size: 28rpx;
		margin-right: 12rpx;
	}

	/* 优化调色板网格布局 */
	.color-palette .grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-around;
		align-items: center;
		padding: 10rpx 0;
		gap: 10rpx;
	}

	.color-palette .grid.col-5 > .color-item {
		width: 60rpx;
		height: 60rpx;
		flex: 0 0 60rpx;
	}

	.color-item {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		margin: 8rpx auto;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		border: 2rpx solid transparent;
	}

	.color-item:active {
		transform: scale(0.95);
	}

	.color-item:hover {
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.25);
		transform: translateY(-2rpx);
	}

	/* 确保调色板在小屏幕上的显示效果 */
	@media screen and (max-width: 750rpx) {
		.color-palette .grid {
			gap: 8rpx;
		}

		.color-item {
			width: 50rpx;
			height: 50rpx;
			margin: 6rpx auto;
		}

		.color-palette .grid.col-5 > .color-item {
			width: 50rpx;
			height: 50rpx;
			flex: 0 0 50rpx;
		}
	}

	/* 修复可能的布局问题 */
	.color-palette .text-center {
		text-align: center;
	}

	/* 语音区域样式 */
	.audio-card {
		background: #ffffff;
		margin: 20rpx 0rpx;
		border-radius: 24rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.1);
	}

	.audio-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.audio-player {
		margin-bottom: 30rpx;
	}

	.audio-controls {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.play-btn,
	.pause-btn {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #1cbbb4, #36cfc9);
		color: white;
		font-size: 40rpx;
		box-shadow: 0 4rpx 16rpx rgba(28, 187, 180, 0.3);
	}

	.audio-slider {
		flex: 1;
	}

	.time-display {
		display: flex;
		justify-content: space-between;
		margin-top: 12rpx;
		font-size: 24rpx;
	}

	.current-time {
		color: #1cbbb4;
	}

	.total-time {
		color: #666666;
	}

	.recording-area {
		text-align: center;
		margin: 40rpx 0;
	}

	.record-btn,
	.stop-btn {
		display: inline-flex;
		flex-direction: column;
		align-items: center;
		background: linear-gradient(135deg, #1cbbb4, #36cfc9);
		color: white;
		padding: 50rpx;
		border-radius: 50%;
		box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.3);
		transition: transform 0.2s ease;
	}

	.record-btn:active,
	.stop-btn:active {
		transform: scale(0.95);
	}

	.record-emoji,
	.stop-emoji {
		font-size: 48rpx;
	}

	.record-text,
	.stop-text {
		font-size: 24rpx;
		font-weight: 500;
	}

	.recording-tip {
		text-align: center;
		color: #666666;
		font-size: 24rpx;
		margin-top: 20rpx;
	}

	/* 视频区域样式 */
	.video-card {
		background: #ffffff;
		margin: 20rpx 0rpx;
		border-radius: 24rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.1);
	}

	.video-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.video-preview {
		width: 100%;
		border-radius: 16rpx;
		margin-bottom: 30rpx;
	}

	.video-options {
		display: flex;
		gap: 20rpx;
		margin-bottom: 30rpx;
	}

	.video-option {
		flex: 1;
		text-align: center;
		background: #f8f9fa;
		border-radius: 20rpx;
		padding: 30rpx 20rpx;
		transition: all 0.3s ease;
	}

	.video-option:active {
		background: #e9ecef;
		transform: translateY(2rpx);
	}

	.option-icon {
		margin-bottom: 16rpx;
	}
	.option-text {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
		margin-bottom: 8rpx;
	}

	.option-desc {
		font-size: 22rpx;
		color: #666666;
	}

	/* 视频输入和设置区域 */
	.url-input-section,
	.shipinhao-section {
		background: #f8f9fa;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-top: 20rpx;
	}

	.input-header,
	.shipinhao-header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}

	.input-emoji {
		font-size: 28rpx;
		margin-right: 12rpx;
	}

	.url-textarea {
		background: #ffffff;
		border: 2rpx solid #e9ecef;
		border-radius: 16rpx;
		padding: 24rpx;
		font-size: 28rpx;
		color: #333333;
		width: 100%;
		min-height: 120rpx;
		margin-bottom: 20rpx;
	}

	.parse-btn-container,
	.preview-btn-container {
		text-align: center;
	}

	.parse-btn,
	.preview-btn {
		display: inline-flex;
		align-items: center;
		gap: 12rpx;
		background: linear-gradient(135deg, #1cbbb4, #36cfc9);
		color: white;
		padding: 20rpx 40rpx;
		border-radius: 25rpx;
		font-size: 28rpx;
		font-weight: 500;
		box-shadow: 0 4rpx 16rpx rgba(28, 187, 180, 0.3);
		border: none;
	}

	.tip-text {
		color: #ff6b6b;
		font-size: 24rpx;
		margin: 20rpx 0;
	}

	.remark-section {
		display: flex;
		align-items: center;
		gap: 12rpx;
		background: #f0f8ff;
		border-radius: 16rpx;
		padding: 20rpx;
		margin-top: 20rpx;
	}

	.remark-emoji {
		font-size: 24rpx;
	}

	.remark-text {
		font-size: 24rpx;
		color: #666666;
		line-height: 1.5;
	}

	/* 功能按钮区域 */
	.function-buttons-card {
		background: #ffffff;
		margin: 20rpx 0rpx;
		border-radius: 24rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.1);
	}

	.function-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.function-grid {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20rpx;
	}

	.function-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 12rpx;
		background: #f8f9fa;
		border-radius: 20rpx;
		padding: 30rpx 20rpx;
		transition: all 0.3s ease;
		border: 2rpx solid transparent;
	}

	.function-item.active {
		background: linear-gradient(135deg, #e8f4fd, #f0f8ff);
		border-color: #1cbbb4;
		box-shadow: 0 4rpx 16rpx rgba(28, 187, 180, 0.2);
	}

	.function-item:active {
		transform: translateY(2rpx);
	}

	.function-emoji {
		font-size: 40rpx;
	}

	.function-text {
		font-size: 24rpx;
		color: #333333;
		font-weight: 500;
	}

	/* 投票区域样式 */
	.vote-card {
		background: #ffffff;
		margin: 20rpx;
		border-radius: 24rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.1);
	}

	.vote-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.vote-options {
		margin-bottom: 30rpx;
	}

	.vote-option {
		margin-bottom: 24rpx;
	}

	.option-label {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}

	.option-emoji {
		font-size: 60rpx;
		margin-right: 12rpx;
	}

	.vote-settings {
		background: #f8f9fa;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
	}

	.setting-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24rpx;
	}

	.setting-item:last-child {
		margin-bottom: 0;
	}

	.setting-emoji {
		font-size: 28rpx;
		margin-right: 12rpx;
	}

	.setting-label {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}

	.modern-switch {
		transform: scale(1.2);
	}

	.vote-actions {
		display: flex;
		gap: 20rpx;
		justify-content: center;
	}

	.action-btn {
		display: flex;
		align-items: center;
		gap: 12rpx;
		padding: 20rpx 40rpx;
		border-radius: 25rpx;
		font-size: 28rpx;
		font-weight: 500;
		border: none;
		transition: all 0.3s ease;
	}

	.add-btn {
		background: linear-gradient(135deg, #52c41a, #73d13d);
		color: white;
		box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.3);
	}

	.remove-btn {
		background: linear-gradient(135deg, #ff7875, #ff9c6e);
		color: white;
		box-shadow: 0 4rpx 16rpx rgba(255, 120, 117, 0.3);
	}

	.btn-emoji {
		font-size: 24rpx;
	}

	/* 活动区域样式 */
	.activity-card {
		background: #ffffff;
		margin: 20rpx 0rpx;
		border-radius: 24rpx;
		padding: 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.1);
	}

	.activity-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.activity-item {
		background: #f8f9fa;
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;
		transition: all 0.3s ease;
	}

	.activity-item:active {
		background: #e9ecef;
	}

	.activity-label {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}

	.activity-emoji {
		font-size: 28rpx;
		margin-right: 12rpx;
	}

	.activity-value {
		font-size: 26rpx;
		color: #666666;
		margin-top: 8rpx;
	}

	.activity-tip {
		display: flex;
		align-items: center;
		gap: 12rpx;
		background: #f0f8ff;
		border-radius: 16rpx;
		padding: 20rpx;
		margin-top: 20rpx;
	}

	.tip-emoji {
		font-size: 24rpx;
	}

	.tip-text {
		font-size: 24rpx;
		color: #666666;
	}

	/* 发布按钮样式 */
	.publish-btn {
		background: linear-gradient(135deg, #1cbbb4, #36cfc9) !important;
		color: white !important;
		border-radius: 50rpx !important;
		font-size: 32rpx !important;
		font-weight: 600 !important;
		padding: 30rpx 0 !important;
		margin: 40rpx 20rpx !important;
		box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.3) !important;
		border: none !important;
		transition: all 0.3s ease !important;
		height: 50px;
	}

	.publish-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(28, 187, 180, 0.3) !important;
	}

	.publish-btn[disabled] {
		background: #cccccc !important;
		box-shadow: none !important;
		transform: none !important;
	}

	/* 发帖须知样式 */
	.post-notice {
		text-align: center;
		padding: 40rpx 20rpx;
	}

	.notice-content {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12rpx;
		color: #ff9966;
		font-size: 28rpx;
		padding-bottom: 100rpx;
	}

	.notice-emoji {
		font-size: 28rpx;
	}

	/* 模态框优化 */
	.cu-modal .cu-dialog {
		border-radius: 24rpx !important;
		overflow: hidden;
	}

	.cu-modal .cu-bar {
		background: linear-gradient(135deg, #1cbbb4, #36cfc9) !important;
		color: white !important;
	}

	.cu-modal .cu-bar .content {
		color: white !important;
		font-weight: 600;
	}

	.cu-modal .padding {
		padding: 30rpx !important;
		font-size: 28rpx;
		line-height: 1.6;
	}

	.cu-modal .cu-btn {
		border-radius: 20rpx !important;
		font-weight: 500;
	}

	/* 字体颜色选择模态框样式 */
	.color-picker-modal {
		backdrop-filter: blur(10rpx);
		background: rgba(28, 187, 180, 0.3) !important;
	}

	.color-picker-dialog {
		background: linear-gradient(145deg, #ffffff, #f8f9ff) !important;
		border-radius: 32rpx !important;
		box-shadow: 0 20rpx 60rpx rgba(28, 187, 180, 0.15),
					0 8rpx 24rpx rgba(28, 187, 180, 0.1) !important;
		overflow: hidden;
		max-width: 600rpx;
		margin: 0 auto;
		border: 2rpx solid rgba(28, 187, 180, 0.1);
	}

	.color-picker-header {
		background: linear-gradient(135deg, #1cbbb4 0%, #36cfc9 50%, #5cdbd3 100%);
		padding: 30rpx 40rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
	}

	.color-picker-header::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 100%);
		pointer-events: none;
	}

	.header-content {
		display: flex;
		align-items: center;
		gap: 16rpx;
		z-index: 1;
	}

	.header-icon {
		font-size: 36rpx;
		filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.2));
	}

	.header-title {
		color: #ffffff;
		font-size: 32rpx;
		font-weight: 600;
		text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
	}

	.close-btn {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.2);
		display: flex;
		align-items: center;
		justify-content: center;
		color: #ffffff;
		font-size: 32rpx;
		transition: all 0.3s ease;
		z-index: 1;
		backdrop-filter: blur(10rpx);
	}

	.close-btn:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}

	.color-picker-body {
		padding: 40rpx 30rpx;
		background: linear-gradient(145deg, #ffffff, #f8f9ff);
	}

	.color-grid {
		display: grid;
		grid-template-columns: repeat(5, 1fr);
		gap: 20rpx;
		justify-items: center;
	}

	.color-option {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		position: relative;
		cursor: pointer;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15),
					0 2rpx 6rpx rgba(0, 0, 0, 0.1);
		border: 3rpx solid #ffffff;
	}

	.color-option::before {
		content: '';
		position: absolute;
		top: -6rpx;
		left: -6rpx;
		right: -6rpx;
		bottom: -6rpx;
		border-radius: 50%;
		background: linear-gradient(45deg, transparent, rgba(255,255,255,0.8), transparent);
		opacity: 0;
		transition: opacity 0.3s ease;
		z-index: -1;
	}

	.color-option:active {
		transform: scale(0.9);
	}

	.color-option:hover::before {
		opacity: 1;
	}

	.color-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: 50%;
		background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, transparent 50%, rgba(0,0,0,0.1) 100%);
		pointer-events: none;
	}

	.color-picker-footer {
		padding: 30rpx 40rpx;
		background: linear-gradient(145deg, #f8f9ff, #ffffff);
		border-top: 1rpx solid rgba(28, 187, 180, 0.1);
		display: flex;
		justify-content: center;
	}

	.cancel-btn {
		display: flex;
		align-items: center;
		gap: 12rpx;
		padding: 20rpx 40rpx;
		background: linear-gradient(135deg, #e8f4fd, #f0f8ff);
		color: #1cbbb4;
		border: 2rpx solid #1cbbb4;
		border-radius: 25rpx;
		font-size: 28rpx;
		font-weight: 500;
		transition: all 0.3s ease;
		box-shadow: 0 4rpx 12rpx rgba(28, 187, 180, 0.2);
	}

	.cancel-btn:active {
		transform: translateY(2rpx);
		background: linear-gradient(135deg, #d2f1f0, #e8f4fd);
		box-shadow: 0 2rpx 6rpx rgba(28, 187, 180, 0.3);
	}

	.btn-icon {
		font-size: 24rpx;
	}

	/* 响应式优化 */
	@media (max-width: 750rpx) {
		.function-grid {
			grid-template-columns: repeat(2, 1fr);
		}

		.video-options {
			flex-direction: column;
		}

		.vote-actions {
			flex-direction: column;
		}

		/* 颜色选择器响应式 */
		.color-picker-dialog {
			max-width: 90vw;
			margin: 0 20rpx;
		}

		.color-picker-header {
			padding: 25rpx 30rpx;
		}

		.header-title {
			font-size: 28rpx;
		}

		.color-picker-body {
			padding: 30rpx 20rpx;
		}

		.color-grid {
			gap: 15rpx;
		}

		.color-option {
			width: 60rpx;
			height: 60rpx;
		}

		.color-picker-footer {
			padding: 25rpx 30rpx;
		}

		.cancel-btn {
			padding: 16rpx 32rpx;
			font-size: 26rpx;
		}
	}

	._add_iocn_size {
		font-size: 50rpx;
		vertical-align: middle;
	}

	._add_text_size {
		font-size: 30rpx;
		color: #888;
	}

	.DrawerPage {
		position: fixed;
		width: 100vw;
		height: 100vh;
		left: 0vw;
		background-color: #f1f1f1;
		transition: all 0.4s;
	}

	.cu-form-group+.cu-form-group {
		border: 0px !important;
	}

	.DrawerPage.show {
		transform: scale(0.9, 0.9);
		left: 85vw;
		box-shadow: 0 0 60rpx rgba(0, 0, 0, 0.2);
		transform-origin: 0;
	}

	.DrawerWindow {
		background-color: #fff;
		position: fixed;
		width: 85vw;
		height: 90vh;
		left: 0;
		top: 10%;
		transform: scale(0.9, 0.9) translateX(-100%);
		opacity: 0;
		pointer-events: none;
		transition: all 0.4s;
		z-index: 200;
	}

	.DrawerWindow.show {
		/* margin-top: 160rpx; */
		transform: scale(1, 1) translateX(0%);
		opacity: 1;
		pointer-events: all;
	}

	.DrawerClose {
		position: fixed;
		width: 40vw;
		height: 100vh;
		right: 0;
		top: 0;
		color: transparent;
		padding-bottom: 30rpx;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.6));
		letter-spacing: 5px;
		font-size: 50rpx;
		opacity: 0;
		pointer-events: none;
		transition: all 0.4s;
	}

	.DrawerClose.show {
		opacity: 1;
		pointer-events: all;
		width: 15vw;
		color: #fff;
	}

	.DrawerPage .cu-bar.tabbar .action button.icon {
		width: 64rpx;
		height: 64rpx;
		line-height: 64rpx;
		margin: 0;
		display: inline-block;
	}

	.DrawerPage .cu-bar.tabbar .action .cu-avatar {
		margin: 0;
	}

	.DrawerPage .nav {
		flex: 1;
	}

	.DrawerPage .nav .cu-item.cur {
		border-bottom: 0;
		position: relative;
	}

	.DrawerPage .nav .cu-item.cur::after {
		content: "";
		width: 10rpx;
		height: 10rpx;
		background-color: currentColor;
		position: absolute;
		bottom: 10rpx;
		border-radius: 10rpx;
		left: 0;
		right: 0;
		margin: auto;
	}

	.DrawerPage .cu-bar.tabbar .action {
		flex: initial;
	}













	/* pages/index.wxss */
	.container {
		width: 100%;
		padding: 16rpx;
		box-sizing: border-box;
	}



	.placeholder-class {
		font-size: 14px;
		font-weight: 400;
		color: rgba(204, 204, 204, 1);
		line-height: 40rpx;
	}


	.image-drag-container {
		padding: 32rpx 0;
		box-sizing: border-box;
	}

	.movable-area {
		width: 100%;
	}

	.image-choose-container {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: space-between;
		box-sizing: border-box;
	}

	.image-choose-container>view {
		position: relative;
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 16rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background: rgba(248, 248, 248, 1);
		border-radius: 12rpx;
		box-sizing: border-box;
		overflow: hidden;
	}

	.image-choose-container .image-item-temp {
		height: 0;
		background: transparent;
	}

	.image-choose-container>view image {
		width: 100%;
		height: 100%;
	}

	.image-choose-container>view .close {
		position: absolute;
		top: 0;
		right: 0;
		width: 40rpx;
		height: 40rpx;
		font-size: 24rpx;
		text-align: center;
		line-height: 40rpx;
		color: #fff;
		background: rgba(0, 0, 0, 0.4);
		border-radius: 50%;
	}

	.movable-view {
		/* border:1px solid rgb(0, 0, 0); */
		border-radius: 12rpx;
		box-sizing: border-box;
		background-color: rgb(255, 255, 255, 0.5);
	}

	.movable-view image {
		width: 100%;
		height: 100%;
	}

	.re {
		-moz-transform: rotate(-90deg);
		-webkit-transform: rotate(-90deg);
		transform: rotate(-90deg);
		display: inline-block;
	}

	/**index.wxss**/
	.audiosBox {
		width: 92%;
		margin: auto;
		height: 130rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #f6f7f7;
		border-radius: 10rpx;
	}

	/*按钮大小  */
	.audioOpen {
		width: 70rpx;
		height: 70rpx;
		border: 2px solid #4c9dee;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 20rpx;
	}

	.image2 {
		margin-left: 10%;
	}

	/*进度条长度  */
	.slid {
		flex: 1;
		position: relative;
	}

	.slid view {
		display: flex;
		justify-content: space-between;
	}

	.slid view>text:nth-child(1) {
		color: #4c9dee;
		margin-left: 6rpx;
	}

	.slid view>text:nth-child(2) {
		margin-right: 6rpx;
	}

	slider {
		width: 520rpx;
		margin: 0;
		margin-left: 35rpx;
	}

	/*横向布局  */
	.times {
		width: 100rpx;
		text-align: center;
		display: inline-block;
		font-size: 24rpx;
		color: #999999;
		margin-top: 5rpx;
	}

	.title view {
		text-indent: 2em;
	}
</style>